import sys
import json
import time
import pandas as pd
import logging
import requests
import threading
import websocket
from datetime import datetime
import schedule
import signal
import mysql.connector
from mysql.connector import pooling
import json
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.ticker import FuncFormatter
from datetime import datetime, timedelta
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("binance_orderbook.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MariaDB 配置
MARIADB_HOST = "***********"     # MariaDB服务器地址
MARIADB_PORT = 3306              # MariaDB服务器端口
MARIADB_USER = "remote_user"     # MariaDB用户名
MARIADB_PASSWORD = "wyl1975"     # MariaDB密码
MARIADB_DATABASE = "test_db"     # MariaDB数据库名
MARIADB_TABLE = "order_books_ms"    # MariaDB表名
MARIADB_INIT_TABLE = "initial_orderbooks"  # 初始盘口数据表
MARIADB_UPDATE_TABLE = "depth_updates"     # 深度更新表
MARIADB_AGGTRADE_TABLE = "agg_trades"      # 聚合交易数据表
# 新增表 - 用于存储Context API 7中的盘口快照和更新数据的统一表
MARIADB_CONTEXT_TABLE = "orderbook_context"  # 上下文数据存储表
MARIADB_CONTEXT_STATE = "context_state"     # 状态数据表

# Binance API 配置
BINANCE_WSS = "wss://fstream.binance.com/ws"  # Binance期货WebSocket地址
BINANCE_REST = "https://fapi.binance.com"     # Binance期货REST API地址

# 全局变量
ws = None                   # WebSocket连接对象
ws_thread = None            # WebSocket线程对象
reconnect_count = 0         # 重连计数器
max_reconnect_attempts = 20 # 最大重连次数
is_running = True           # 程序运行状态标志
depth_snapshots = []        # 存储深度快照，每分钟写入数据库
lock = threading.Lock()     # 用于确保线程安全的锁
reconnect_event = threading.Event()  # 用于通知线程进行重连的事件
last_rest_call_time = 0  # 上次调用REST API的时间
REST_CALL_INTERVAL = 1  # REST API调用的最小间隔时间(秒)
depth_cache = []  # 用于缓存深度更新
ws_connected = False  # WebSocket连接状态标志
SYMBOL = "solusdt"    # 当前监控的交易对，默认初始值
last_symbol_change_time = 0  # 上次更换监控交易对的时间
SYMBOL_CHANGE_INTERVAL = 7200  # 更换交易对的时间间隔（2小时 = 7200秒）
symbol_change_lock = threading.Lock()  # 用于交易对切换的锁
is_running = True  # 程序运行状态标志

# 创建MariaDB连接池
try:
    mariadb_pool = pooling.MySQLConnectionPool(
        pool_name="mariadb_pool",
        pool_size=5,
        host=MARIADB_HOST,
        port=MARIADB_PORT,
        user=MARIADB_USER,
        password=MARIADB_PASSWORD,
        database=MARIADB_DATABASE
    )
    logger.info("MariaDB连接池创建成功")
except Exception as e:
    logger.error(f"创建MariaDB连接池失败: {e}", exc_info=True)
    mariadb_pool = None

# 订单簿数据结构
class OrderBook:
    def __init__(self, symbol):
        self.symbol = symbol
        self.bids = {}           # 买单 {price: quantity}
        self.asks = {}           # 卖单 {price: quantity}
        self.last_update_id = 0  # 最后更新ID
        self.prev_update_id = 0  # 前一次更新ID
        self.initialized = False # 是否已初始化
        self.snapshots = []      # 深度快照列表
        self.snapshot_timer = None # 快照定时器

        
    def restore_from_state(self, state_data):
        """
        从保存的状态数据恢复订单簿
        
        参数:
            state_data (dict): 包含订单簿状态的字典
        
        返回:
            bool: 是否成功恢复
        """
        try:
            if not state_data or not isinstance(state_data, dict):
                logger.warning("无法从无效的状态数据恢复订单簿")
                return False
                
            # 验证状态数据是否与当前交易对匹配
            if state_data.get('symbol', '').lower() != self.symbol.lower():
                logger.warning(f"状态数据的交易对 {state_data.get('symbol')} 与当前交易对 {self.symbol} 不匹配")
                return False
                
            # 恢复买卖盘数据
            bids_data = state_data.get('bids', [])
            asks_data = state_data.get('asks', [])
            
            # 清空当前数据
            self.bids = {}
            self.asks = {}
            
            # 恢复买单数据
            for price_qty in bids_data:
                if len(price_qty) >= 2:
                    price = float(price_qty[0])
                    qty = float(price_qty[1])
                    if qty > 0:
                        self.bids[price] = qty
            
            # 恢复卖单数据
            for price_qty in asks_data:
                if len(price_qty) >= 2:
                    price = float(price_qty[0])
                    qty = float(price_qty[1])
                    if qty > 0:
                        self.asks[price] = qty
            
            # 恢复更新ID和初始化标志
            self.last_update_id = state_data.get('last_update_id', 0)
            self.prev_update_id = state_data.get('prev_update_id', 0)
            self.initialized = state_data.get('initialized', False)
            
            logger.info(f"成功从保存的状态恢复 {self.symbol.upper()} 订单簿，" 
                      f"买单: {len(self.bids)}档，卖单: {len(self.asks)}档，" 
                      f"最后更新ID: {self.last_update_id}")
                      
            return True
            
        except Exception as e:
            logger.error(f"从状态恢复订单簿失败: {e}", exc_info=True)
            return False

# 全局订单簿对象
orderbook = OrderBook(SYMBOL)

def init_mariadb():
    """
    初始化MariaDB数据库和表
    
    创建数据库和表结构（如果不存在）
    """
    if mariadb_pool is None:
        logger.error("MariaDB连接池不可用，无法初始化数据库")
        return False
    
    try:
        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor()
        
        # 创建数据库（如果不存在）
        # cursor.execute(f"CREATE DATABASE IF NOT EXISTS {MARIADB_DATABASE}")
        cursor.execute(f"USE {MARIADB_DATABASE}")
        
        # 创建表（如果不存在）
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {MARIADB_TABLE} (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            timestamp DATETIME NOT NULL,
            timestamp_ms BIGINT NOT NULL,
            update_id BIGINT NOT NULL,
            bids_json LONGTEXT NOT NULL,
            asks_json LONGTEXT NOT NULL,
            best_bid_price DOUBLE NOT NULL,
            best_ask_price DOUBLE NOT NULL,
            bid_volume DOUBLE NOT NULL,
            ask_volume DOUBLE NOT NULL,
            spread DOUBLE NOT NULL,
            spread_percent DOUBLE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (symbol, timestamp),
            INDEX (timestamp_ms)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        cursor.execute(create_table_sql)
        
        # 创建初始盘口数据表
        create_init_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {MARIADB_INIT_TABLE} (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            timestamp DATETIME NOT NULL,
            timestamp_ms BIGINT NOT NULL,
            last_update_id BIGINT NOT NULL,
            bids_json LONGTEXT NOT NULL,
            asks_json LONGTEXT NOT NULL,
            bids_count INT NOT NULL,
            asks_count INT NOT NULL,
            best_bid_price DOUBLE NOT NULL,
            best_ask_price DOUBLE NOT NULL,
            bid_volume DOUBLE NOT NULL,
            ask_volume DOUBLE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (symbol, timestamp),
            INDEX (timestamp_ms),
            INDEX (last_update_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        cursor.execute(create_init_table_sql)
        
        # 创建深度更新表
        create_update_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {MARIADB_UPDATE_TABLE} (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            timestamp DATETIME NOT NULL,
            timestamp_ms BIGINT NOT NULL,
            first_update_id BIGINT NOT NULL COMMENT '起始更新ID(U)',
            final_update_id BIGINT NOT NULL COMMENT '最终更新ID(u)',
            prev_final_id BIGINT NOT NULL COMMENT '前一个最终更新ID(pu)',
            bids_json LONGTEXT NOT NULL,
            asks_json LONGTEXT NOT NULL,
            bids_count INT NOT NULL,
            asks_count INT NOT NULL,
            is_snapshot BOOLEAN DEFAULT FALSE COMMENT '是否为快照数据',
            event_time BIGINT COMMENT '事件时间戳(E)',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (symbol, timestamp),
            INDEX (timestamp_ms),
            INDEX (final_update_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        cursor.execute(create_update_table_sql)
        
        # 创建聚合交易表
        create_aggtrade_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {MARIADB_AGGTRADE_TABLE} (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            event_time BIGINT NOT NULL COMMENT '事件时间(E)',
            agg_trade_id BIGINT NOT NULL COMMENT '归集成交ID(a)',
            price DOUBLE NOT NULL COMMENT '成交价格(p)',
            quantity DOUBLE NOT NULL COMMENT '成交量(q)',
            first_trade_id BIGINT NOT NULL COMMENT '被归集的首个交易ID(f)',
            last_trade_id BIGINT NOT NULL COMMENT '被归集的末次交易ID(l)',
            trade_time BIGINT NOT NULL COMMENT '成交时间(T)',
            is_buyer_maker BOOLEAN NOT NULL COMMENT '买方是否是做市方(m)',
            timestamp DATETIME NOT NULL,
            timestamp_ms BIGINT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (symbol, trade_time),
            INDEX (timestamp_ms),
            INDEX (event_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        cursor.execute(create_aggtrade_table_sql)
        
        # 创建新的Context API盘口数据统一表
        create_context_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {MARIADB_CONTEXT_TABLE} (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            timestamp DATETIME NOT NULL,
            timestamp_ms BIGINT NOT NULL,
            update_id BIGINT NOT NULL,
            data_type ENUM('snapshot', 'update') NOT NULL COMMENT '数据类型：快照或更新',
            bids_json LONGTEXT NOT NULL,
            asks_json LONGTEXT NOT NULL,
            bids_count INT NOT NULL,
            asks_count INT NOT NULL,
            best_bid_price DOUBLE NOT NULL,
            best_ask_price DOUBLE NOT NULL,
            bid_volume DOUBLE NOT NULL,
            ask_volume DOUBLE NOT NULL,
            spread DOUBLE NOT NULL,
            spread_percent DOUBLE NOT NULL,
            event_time BIGINT COMMENT '事件时间戳(E)',
            first_update_id BIGINT COMMENT '起始更新ID(U)',
            final_update_id BIGINT COMMENT '最终更新ID(u)',
            prev_final_id BIGINT COMMENT '前一个最终更新ID(pu)',
            metadata JSON COMMENT '其他元数据和扩展信息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (symbol, timestamp),
            INDEX (timestamp_ms),
            INDEX (update_id),
            INDEX (data_type),
            INDEX (event_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        cursor.execute(create_context_table_sql)
        
        # 创建上下文状态表 - 用于保存最新的状态和恢复点
        create_state_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {MARIADB_CONTEXT_STATE} (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL,
            last_update_timestamp DATETIME NOT NULL,
            last_update_ms BIGINT NOT NULL,
            last_update_id BIGINT NOT NULL,
            current_state JSON NOT NULL COMMENT '当前状态的序列化数据',
            session_id VARCHAR(50) NOT NULL COMMENT '会话标识符',
            is_valid BOOLEAN DEFAULT TRUE COMMENT '状态是否有效',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY (symbol, session_id),
            INDEX (last_update_timestamp),
            INDEX (last_update_ms)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """
        cursor.execute(create_state_table_sql)
        
        # 提交事务
        conn.commit()
        
        logger.info("MariaDB数据库和表已初始化")
        
        # 关闭连接
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"初始化MariaDB失败: {e}", exc_info=True)
        return False

def save_to_context_table(data, data_type='snapshot'):
    """
    将数据保存到Context统一表
    
    参数:
        data (dict): 要保存的数据
        data_type (str): 数据类型，'snapshot'或'update'
    
    返回:
        bool: 是否成功保存
    """
    if mariadb_pool is None:
        logger.error("MariaDB连接池不可用，无法保存到Context表")
        return False
    
    try:
        # 获取当前时间
        timestamp = datetime.now()
        timestamp_ms = int(time.time() * 1000)
        
        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor()
        
        if data_type == 'snapshot':
            # 处理快照数据
            bids = data.get('bids', [])
            asks = data.get('asks', [])
            update_id = data.get('lastUpdateId', 0)
            
            # 计算统计指标
            bids_count = len(bids)
            asks_count = len(asks)
            
            # 计算最佳买卖价和总量
            best_bid_price = float(bids[0][0]) if bids else 0
            best_ask_price = float(asks[0][0]) if asks else 0
            
            bid_volume = sum(float(bid[1]) for bid in bids)
            ask_volume = sum(float(ask[1]) for ask in asks)
            
            # 计算价差
            spread = best_ask_price - best_bid_price
            spread_percent = (spread / best_bid_price * 100) if best_bid_price else 0
            
            # 转换为JSON格式
            bids_json = json.dumps(bids)
            asks_json = json.dumps(asks)
            
            # 准备SQL语句
            insert_sql = f"""
            INSERT INTO {MARIADB_CONTEXT_TABLE} (
                symbol, timestamp, timestamp_ms, update_id, data_type,
                bids_json, asks_json, bids_count, asks_count,
                best_bid_price, best_ask_price, bid_volume, ask_volume,
                spread, spread_percent
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """
            
            values = (
                SYMBOL,                # symbol
                timestamp,             # timestamp
                timestamp_ms,          # timestamp_ms
                update_id,             # update_id
                'snapshot',            # data_type
                bids_json,             # bids_json
                asks_json,             # asks_json
                bids_count,            # bids_count
                asks_count,            # asks_count
                best_bid_price,        # best_bid_price
                best_ask_price,        # best_ask_price
                bid_volume,            # bid_volume
                ask_volume,            # ask_volume
                spread,                # spread
                spread_percent         # spread_percent
            )
            
            # 元数据(可选)
            metadata = {
                'source': 'rest_api',
                'snapshot_time': timestamp_ms
            }
        
        elif data_type == 'update':
            # 处理更新数据
            symbol = data.get('s', '').lower()
            first_update_id = data.get('U', 0)
            final_update_id = data.get('u', 0)
            prev_final_id = data.get('pu', 0)
            event_time = data.get('E', 0)
            
            bids = data.get('b', [])
            asks = data.get('a', [])
            
            # 计算统计指标
            bids_count = len(bids)
            asks_count = len(asks)
            
            # 尝试从现有订单簿中获取最佳价格和数量
            # 如果无法获取，则使用默认值或估算值
            try:
                with lock:
                    best_bid_price = sorted(orderbook.bids.items(), key=lambda x: x[0], reverse=True)[0][0] if orderbook.bids else 0
                    best_ask_price = sorted(orderbook.asks.items(), key=lambda x: x[0])[0][0] if orderbook.asks else 0
                    
                    bid_volume = sum(orderbook.bids.values())
                    ask_volume = sum(orderbook.asks.values())
            except Exception as e:
                logger.warning(f"无法从订单簿获取价格/数量信息: {e}")
                # 尝试从更新数据中估算
                non_zero_bids = [float(b[0]) for b in bids if float(b[1]) > 0]
                non_zero_asks = [float(a[0]) for a in asks if float(a[1]) > 0]
                
                best_bid_price = max(non_zero_bids) if non_zero_bids else 0
                best_ask_price = min(non_zero_asks) if non_zero_asks else 0
                
                # 这里的数量只是更新部分，不代表完整深度
                bid_volume = sum(float(b[1]) for b in bids if float(b[1]) > 0)
                ask_volume = sum(float(a[1]) for a in asks if float(a[1]) > 0)
            
            # 计算价差
            spread = best_ask_price - best_bid_price if best_bid_price > 0 and best_ask_price > 0 else 0
            spread_percent = (spread / best_bid_price * 100) if best_bid_price > 0 else 0
            
            # 转换为JSON格式
            bids_json = json.dumps(bids)
            asks_json = json.dumps(asks)
            
            # 准备SQL语句
            insert_sql = f"""
            INSERT INTO {MARIADB_CONTEXT_TABLE} (
                symbol, timestamp, timestamp_ms, update_id, data_type,
                bids_json, asks_json, bids_count, asks_count,
                best_bid_price, best_ask_price, bid_volume, ask_volume,
                spread, spread_percent, event_time, first_update_id,
                final_update_id, prev_final_id
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """
            
            values = (
                symbol,                # symbol
                timestamp,             # timestamp
                timestamp_ms,          # timestamp_ms
                final_update_id,       # update_id (使用最终更新ID)
                'update',              # data_type
                bids_json,             # bids_json
                asks_json,             # asks_json
                bids_count,            # bids_count
                asks_count,            # asks_count
                best_bid_price,        # best_bid_price
                best_ask_price,        # best_ask_price
                bid_volume,            # bid_volume
                ask_volume,            # ask_volume
                spread,                # spread
                spread_percent,        # spread_percent
                event_time,            # event_time
                first_update_id,       # first_update_id
                final_update_id,       # final_update_id
                prev_final_id          # prev_final_id
            )
        
        else:
            logger.error(f"不支持的数据类型: {data_type}")
            return False
        
        # 执行SQL
        cursor.execute(insert_sql, values)
        
        # 提交事务
        conn.commit()
        
        data_type_cn = "快照" if data_type == 'snapshot' else "更新"
        logger.info(f"成功保存 {SYMBOL.upper()} {data_type_cn}数据到Context表")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"保存到Context表失败: {e}", exc_info=True)
        return False

def update_context_state():
    """
    更新上下文状态表，保存当前订单簿状态
    
    返回:
        bool: 是否成功更新
    """
    if mariadb_pool is None:
        logger.error("MariaDB连接池不可用，无法更新上下文状态")
        return False
    
    try:
        # 获取当前时间和会话ID
        timestamp = datetime.now()
        timestamp_ms = int(time.time() * 1000)
        session_id = f"{SYMBOL}_{int(time.time())}"  # 使用交易对和时间戳生成会话ID
        
        with lock:
            # 准备订单簿状态数据
            orderbook_state = {
                'symbol': orderbook.symbol,
                'bids': [[price, qty] for price, qty in orderbook.bids.items()],
                'asks': [[price, qty] for price, qty in orderbook.asks.items()],
                'last_update_id': orderbook.last_update_id,
                'prev_update_id': orderbook.prev_update_id,
                'initialized': orderbook.initialized,
                'timestamp': timestamp_ms
            }
            
            # 序列化为JSON
            state_json = json.dumps(orderbook_state)
        
        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor()
        
        # 检查是否已存在该交易对和会话的记录
        check_sql = f"""
        SELECT id FROM {MARIADB_CONTEXT_STATE}
        WHERE symbol = %s AND session_id = %s
        """
        cursor.execute(check_sql, (SYMBOL, session_id))
        existing = cursor.fetchone()
        
        if existing:
            # 更新现有记录
            update_sql = f"""
            UPDATE {MARIADB_CONTEXT_STATE} SET
                last_update_timestamp = %s,
                last_update_ms = %s,
                last_update_id = %s,
                current_state = %s,
                is_valid = TRUE
            WHERE symbol = %s AND session_id = %s
            """
            values = (
                timestamp,
                timestamp_ms,
                orderbook.last_update_id,
                state_json,
                SYMBOL,
                session_id
            )
            cursor.execute(update_sql, values)
        else:
            # 插入新记录
            insert_sql = f"""
            INSERT INTO {MARIADB_CONTEXT_STATE} (
                symbol, last_update_timestamp, last_update_ms,
                last_update_id, current_state, session_id, is_valid
            ) VALUES (
                %s, %s, %s, %s, %s, %s, TRUE
            )
            """
            values = (
                SYMBOL,
                timestamp,
                timestamp_ms,
                orderbook.last_update_id,
                state_json,
                session_id
            )
            cursor.execute(insert_sql, values)
        
        # 提交事务
        conn.commit()
        
        logger.info(f"成功更新 {SYMBOL.upper()} 上下文状态数据")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"更新上下文状态失败: {e}", exc_info=True)
        return False

def get_latest_context_state(symbol=None):
    """
    获取最新的上下文状态数据
    
    参数:
        symbol (str): 交易对名称，如不指定则获取当前交易对
        
    返回:
        dict: 状态数据字典，如果不存在则返回None
    """
    if mariadb_pool is None:
        logger.error("MariaDB连接池不可用，无法获取上下文状态")
        return None
    
    symbol = symbol.lower() if symbol else SYMBOL
    
    try:
        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询最新的状态记录
        query_sql = f"""
        SELECT * FROM {MARIADB_CONTEXT_STATE}
        WHERE symbol = %s AND is_valid = TRUE
        ORDER BY last_update_ms DESC
        LIMIT 1
        """
        cursor.execute(query_sql, (symbol,))
        record = cursor.fetchone()
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        if not record:
            logger.warning(f"未找到 {symbol.upper()} 的上下文状态数据")
            return None
        
        # 解析状态数据
        try:
            state_data = json.loads(record['current_state'])
            logger.info(f"成功加载 {symbol.upper()} 的上下文状态数据，最后更新ID: {record['last_update_id']}")
            return state_data
        except json.JSONDecodeError as e:
            logger.error(f"解析状态数据失败: {e}")
            return None
            
    except Exception as e:
        logger.error(f"获取上下文状态失败: {e}", exc_info=True)
        return None

def save_initial_orderbook(snapshot):
    """
    将初始盘口数据保存到MariaDB

    参数:
        snapshot: REST API获取的深度快照数据
    """
    if mariadb_pool is None:
        logger.error("MariaDB连接池不可用，无法保存初始盘口数据")
        return False

    try:
        # 获取当前时间
        timestamp = datetime.now()
        timestamp_ms = int(time.time() * 1000)

        # 获取买卖盘数据
        bids = snapshot.get('bids', [])
        asks = snapshot.get('asks', [])
        last_update_id = snapshot.get('lastUpdateId', 0)

        # 计算统计指标
        bids_count = len(bids)
        asks_count = len(asks)

        # 计算最佳买卖价和总量
        best_bid_price = float(bids[0][0]) if bids else 0
        best_ask_price = float(asks[0][0]) if asks else 0

        bid_volume = sum(float(bid[1]) for bid in bids)
        ask_volume = sum(float(ask[1]) for ask in asks)

        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor()

        # 转换为JSON格式
        bids_json = json.dumps(bids)
        asks_json = json.dumps(asks)

        # 准备SQL语句
        insert_sql = f"""
        INSERT INTO {MARIADB_INIT_TABLE} (
            symbol, timestamp, timestamp_ms, last_update_id, 
            bids_json, asks_json, bids_count, asks_count,
            best_bid_price, best_ask_price, bid_volume, ask_volume
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        # 执行SQL
        values = (
            SYMBOL,
            timestamp,
            timestamp_ms,
            last_update_id,
            bids_json,
            asks_json,
            bids_count,
            asks_count,
            best_bid_price,
            best_ask_price,
            bid_volume,
            ask_volume
        )

        cursor.execute(insert_sql, values)
        conn.commit()

        logger.info(
            f"成功保存 {SYMBOL.upper()} 初始盘口数据到MariaDB，买单: {bids_count}档，卖单: {asks_count}档，更新ID: {last_update_id}")

        # 关闭连接
        cursor.close()
        conn.close()

        # 同时保存到Context表
        save_to_context_table(snapshot, 'snapshot')
        
        # 更新状态表
        update_context_state()

        return True
    except Exception as e:
        logger.error(f"保存初始盘口数据失败: {e}", exc_info=True)
        return False

def save_depth_update(update):
    """
    将深度更新数据保存到MariaDB

    参数:
        update: WebSocket接收到的深度更新事件
    """
    if mariadb_pool is None:
        logger.error("MariaDB连接池不可用，无法保存深度更新数据")
        return False

    try:
        # 获取当前时间
        timestamp = datetime.now()
        timestamp_ms = int(time.time() * 1000)

        # 获取更新信息
        symbol = update.get('s', '').lower()
        first_update_id = update.get('U', 0)
        final_update_id = update.get('u', 0)
        prev_final_id = update.get('pu', 0)
        event_time = update.get('E', 0)

        # 获取买卖盘更新
        bids = update.get('b', [])
        asks = update.get('a', [])

        # 计算统计指标
        bids_count = len(bids)
        asks_count = len(asks)

        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor()

        # 转换为JSON格式
        bids_json = json.dumps(bids)
        asks_json = json.dumps(asks)

        # 准备SQL语句
        insert_sql = f"""
        INSERT INTO {MARIADB_UPDATE_TABLE} (
            symbol, timestamp, timestamp_ms, 
            first_update_id, final_update_id, prev_final_id,
            bids_json, asks_json, bids_count, asks_count,
            is_snapshot, event_time
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        # 执行SQL
        values = (
            symbol,
            timestamp,
            timestamp_ms,
            first_update_id,
            final_update_id,
            prev_final_id,
            bids_json,
            asks_json,
            bids_count,
            asks_count,
            False,  # 不是快照数据
            event_time
        )

        cursor.execute(insert_sql, values)
        conn.commit()

        logger.debug(
            f"已保存 {symbol.upper()} 深度更新到MariaDB，买单更新: {bids_count}条，卖单更新: {asks_count}条，更新ID: {first_update_id}-{final_update_id}")

        # 关闭连接
        cursor.close()
        conn.close()
        
        # 同时保存到Context表
        save_to_context_table(update, 'update')
        
        # 定期更新状态表（每50条更新保存一次状态，避免过于频繁）
        if final_update_id % 50 == 0:
            update_context_state()

        return True
    except Exception as e:
        logger.error(f"保存深度更新数据失败: {e}", exc_info=True)
        return False

def save_agg_trade(agg_trade):
    """
    将聚合交易数据保存到MariaDB

    参数:
        agg_trade: WebSocket接收到的聚合交易事件
    """
    if mariadb_pool is None:
        logger.error("MariaDB连接池不可用，无法保存聚合交易数据")
        return False

    try:
        # 获取当前时间
        timestamp = datetime.now()
        timestamp_ms = int(time.time() * 1000)

        # 获取交易信息
        symbol = agg_trade.get('s', '').lower()
        event_time = agg_trade.get('E', 0)
        agg_trade_id = agg_trade.get('a', 0)
        price = float(agg_trade.get('p', 0))
        quantity = float(agg_trade.get('q', 0))
        first_trade_id = agg_trade.get('f', 0)
        last_trade_id = agg_trade.get('l', 0)
        trade_time = agg_trade.get('T', 0)
        is_buyer_maker = agg_trade.get('m', False)

        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor()

        # 准备SQL语句
        insert_sql = f"""
        INSERT INTO {MARIADB_AGGTRADE_TABLE} (
            symbol, event_time, agg_trade_id, price, quantity,
            first_trade_id, last_trade_id, trade_time, is_buyer_maker,
            timestamp, timestamp_ms
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        # 执行SQL
        values = (
            symbol,
            event_time,
            agg_trade_id,
            price,
            quantity,
            first_trade_id,
            last_trade_id,
            trade_time,
            is_buyer_maker,
            timestamp,
            timestamp_ms
        )

        cursor.execute(insert_sql, values)
        conn.commit()

        logger.debug(
            f"已保存 {symbol.upper()} 聚合交易到MariaDB，ID: {agg_trade_id}, 价格: {price}, 数量: {quantity}, 时间: {trade_time}")

        # 关闭连接
        cursor.close()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"保存聚合交易数据失败: {e}", exc_info=True)
        return False

def save_to_mariadb():
    """
    将深度快照数据保存到MariaDB
    """
    global depth_snapshots
    
    logger.info(f"开始保存数据到MariaDB - 当前深度快照数量: {len(depth_snapshots)}")
    
    with lock:
        if not depth_snapshots:
            logger.warning("没有深度快照数据需要保存 - 检查take_snapshot函数是否正常运行")
            return False
        
        snapshots_to_save = depth_snapshots.copy()
        depth_snapshots = []  # 清空全局快照列表
        logger.info(f"准备保存 {len(snapshots_to_save)} 条快照数据，并已清空全局快照列表")
    
    if mariadb_pool is None:
        logger.error("MariaDB连接池不可用，无法保存数据")
        return False
    
    try:
        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor()
        
        # 准备SQL语句
        insert_sql = f"""
        INSERT INTO {MARIADB_TABLE} (
            symbol, timestamp, timestamp_ms, update_id, bids_json, asks_json,
            best_bid_price, best_ask_price, bid_volume, ask_volume,
            spread, spread_percent
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """
        
        # 按交易对分组
        symbol_groups = {}
        for snapshot in snapshots_to_save:
            symbol = snapshot['symbol']
            if symbol not in symbol_groups:
                symbol_groups[symbol] = []
            symbol_groups[symbol].append(snapshot)
        
        # 准备批量插入的数据
        total_count = 0
        
        for symbol, snapshots in symbol_groups.items():
            values = []
            
            for snapshot in snapshots:
                # 将DataFrame转换为JSON
                bids_json = json.dumps(snapshot['bids_data']) if snapshot['bids_data'] else '[]'
                asks_json = json.dumps(snapshot['asks_data']) if snapshot['asks_data'] else '[]'
                
                values.append((
                    snapshot['symbol'],
                    snapshot['timestamp'],
                    snapshot['timestamp_ms'],
                    snapshot['update_id'],
                    bids_json,
                    asks_json,
                    snapshot['best_bid_price'],
                    snapshot['best_ask_price'],
                    snapshot['bid_volume'],
                    snapshot['ask_volume'],
                    snapshot['spread'],
                    snapshot['spread_percent']
                ))
            
            # 执行批量插入
            if values:
                cursor.executemany(insert_sql, values)
                conn.commit()
                count = len(values)
                total_count += count
                logger.info(f"成功保存 {count} 条 {symbol.upper()} 深度快照数据到MariaDB")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        if total_count > 0:
            logger.info(f"共保存了 {total_count} 条深度快照数据到MariaDB")
        
        return True
    except Exception as e:
        logger.error(f"保存到MariaDB失败: {e}", exc_info=True)
        return False

def get_all_futures_symbols(max_retries=3, retry_delay=2):
    """
    获取所有可用的U本位合约交易对
    
    参数:
        max_retries (int): 最大重试次数
        retry_delay (int): 重试间隔（秒）
    
    返回:
        list: 交易对列表
    """
    for attempt in range(1, max_retries + 1):
        try:
            # 检查是否可以调用REST API
            if not can_call_rest_api():
                wait_time = REST_CALL_INTERVAL - (time.time() - last_rest_call_time)
                logger.warning(f"REST API调用过于频繁，等待 {wait_time:.1f} 秒后再尝试获取交易对列表")
                time.sleep(wait_time + 0.5)  # 额外等待0.5秒
                
            # 获取交易所信息
            exchange_info_url = f"{BINANCE_REST}/fapi/v1/exchangeInfo"
            logger.info(f"正在获取币安期货交易对列表 (第 {attempt}/{max_retries} 次尝试)...")
            
            response = requests.get(exchange_info_url, timeout=10)
            if response.status_code != 200:
                logger.error(f"获取交易对列表失败: HTTP {response.status_code} - {response.text}")
                if attempt < max_retries:
                    logger.warning(f"将在 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    continue
                return []
                
            data = response.json()
            
            # 过滤出状态为交易中的交易对
            symbols = []
            for symbol_info in data.get('symbols', []):
                # 只包含状态为TRADING且合约类型为永续合约的交易对
                if (symbol_info.get('status') == 'TRADING' and 
                    symbol_info.get('contractType', '') == 'PERPETUAL'):
                    symbols.append(symbol_info.get('symbol').lower())
            
            logger.info(f"获取到 {len(symbols)} 个交易中的期货交易对")
            return symbols
            
        except requests.exceptions.Timeout:
            logger.error(f"获取交易对列表超时")
            if attempt < max_retries:
                logger.warning(f"将在 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                return []
                
        except Exception as e:
            logger.error(f"获取交易对列表失败: {e}", exc_info=True)
            if attempt < max_retries:
                logger.warning(f"将在 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                return []
    
    return []

def get_symbol_24h_stats(symbols, max_retries=3, retry_delay=2):
    """
    获取交易对24小时价格统计
    
    参数:
        symbols (list): 交易对列表
        max_retries (int): 最大重试次数
        retry_delay (int): 重试间隔（秒）
        
    返回:
        list: 交易对统计信息列表，按波动率降序排序
    """
    for attempt in range(1, max_retries + 1):
        try:
            # 检查是否可以调用REST API
            if not can_call_rest_api():
                wait_time = REST_CALL_INTERVAL - (time.time() - last_rest_call_time)
                logger.warning(f"REST API调用过于频繁，等待 {wait_time:.1f} 秒后再尝试获取24小时统计")
                time.sleep(wait_time + 0.5)  # 等待额外的0.5秒以确保不会触发限制
                
            # 获取所有交易对24小时统计
            ticker_url = f"{BINANCE_REST}/fapi/v1/ticker/24hr"
            logger.info(f"正在获取24小时价格统计 (第 {attempt}/{max_retries} 次尝试)...")
            
            response = requests.get(ticker_url, timeout=10)  # 添加10秒超时
            if response.status_code != 200:
                logger.error(f"获取24小时统计失败: HTTP {response.status_code} - {response.text}")
                if attempt < max_retries:
                    logger.warning(f"将在 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    continue
                return []
                
            all_tickers = response.json()
            logger.info(f"成功获取到 {len(all_tickers)} 个交易对的24小时统计")
            
            # 过滤出我们关注的交易对
            symbol_stats = []
            for ticker in all_tickers:
                symbol = ticker.get('symbol', '').lower()
                if symbol in symbols:
                    try:
                        # 计算价格变化百分比的绝对值作为波动率指标
                        price_change_percent = abs(float(ticker.get('priceChangePercent', 0)))
                        volume = float(ticker.get('quoteVolume', 0))  # 使用USDT交易量
                        
                        # 过滤掉成交量过低的交易对
                        if volume < 1000:  # 忽略24小时成交量低于1万USDT的交易对
                            continue
                            
                        symbol_stats.append({
                            'symbol': symbol,
                            'price_change_percent': price_change_percent,
                            'volume': volume,
                            'high': float(ticker.get('highPrice', 0)),
                            'low': float(ticker.get('lowPrice', 0)),
                            'last_price': float(ticker.get('lastPrice', 0)),
                            'volatility_score': price_change_percent * (volume ** 0.5)  # 计算波动率得分，考虑成交量因素
                        })
                    except (ValueError, TypeError) as e:
                        logger.warning(f"处理{symbol}的统计数据时出错: {e}")
                        continue
            
            # 按波动率得分降序排序
            symbol_stats.sort(key=lambda x: x['volatility_score'], reverse=True)
            
            if not symbol_stats:
                logger.warning("过滤后没有符合条件的交易对，可能是由于成交量过低")
                return []
                
            logger.info(f"已获取并排序 {len(symbol_stats)} 个交易对的24小时统计")
            
            # 输出前10个波动最大的交易对
            for i, stat in enumerate(symbol_stats[:10]):
                logger.info(f"排名{i+1}: {stat['symbol'].upper()} - 波动率: {stat['price_change_percent']:.2f}%, "
                          f"成交量: {stat['volume']:.2f} USDT, 波动率得分: {stat['volatility_score']:.2f}")
            
            return symbol_stats
            
        except requests.exceptions.Timeout:
            logger.error(f"获取24小时统计超时")
            if attempt < max_retries:
                logger.warning(f"将在 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                return []
                
        except Exception as e:
            logger.error(f"获取24小时统计失败: {e}", exc_info=True)
            if attempt < max_retries:
                logger.warning(f"将在 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                return []
    
    return []

def find_most_volatile_symbol(symbol_stats):
    """
    查找24小时内波动最大的交易对
    
    返回:
        str: 波动最大的交易对名称，如果发生错误则返回None
    """
    try:
        # 获取所有交易对
        symbols = get_all_futures_symbols()
        if not symbols:
            logger.error("无法获取交易对列表，使用默认交易对")
            return None
            
        # 获取24小时统计并找出波动最大的
        symbol_stats = get_symbol_24h_stats(symbols)
        if not symbol_stats:
            logger.error("无法获取交易对统计信息，使用默认交易对")
            return None
            
        # 返回波动最大的交易对
        most_volatile = symbol_stats[0]['symbol']
        logger.info(f"当前波动最大的交易对为: {most_volatile.upper()}, "
                 f"24小时价格变化: {symbol_stats[0]['price_change_percent']:.2f}%, "
                 f"成交量: {symbol_stats[0]['volume']:.2f} USDT")
        return most_volatile
    except Exception as e:
        logger.error(f"查找波动最大交易对时发生错误: {e}", exc_info=True)
        return None

def schedule_save():
    """
    设置定时任务，每1分钟保存一次数据到数据库
    """
    # 每1分钟执行一次保存操作
    schedule.every(20).seconds.do(save_to_mariadb)
    
    # 循环检查并运行到期的任务
    while is_running:
        schedule.run_pending()
        time.sleep(1)

def take_snapshot():
    """
    生成订单簿快照
    
    每1秒生成一次盘口快照，记录买卖盘价格和数量
    即使订单簿未正式初始化，只要有数据也尝试生成快照
    """
    global orderbook, depth_snapshots
    
    try:
        with lock:
            # 记录快照前的状态
            logger.info(f"尝试生成快照 - 当前深度快照数量: {len(depth_snapshots)}，订单簿状态: 初始化={orderbook.initialized}，买单数={len(orderbook.bids)}，卖单数={len(orderbook.asks)}")
            
            # 即使未初始化，也检查是否有足够的买卖盘数据
            if len(orderbook.bids) == 0 and len(orderbook.asks) == 0:
                logger.warning("订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档")
                return
            
            # 放宽条件：只要有一边有数据就尝试生成快照
            if len(orderbook.bids) == 0:
                logger.warning("买盘数据为空，但尝试使用仅有的卖盘数据生成快照")
                bids_items = []
            else:
                # 取买盘的前50档
                bids_items = sorted(orderbook.bids.items(), key=lambda x: x[0], reverse=True)[:50]
            
            if len(orderbook.asks) == 0:
                logger.warning("卖盘数据为空，但尝试使用仅有的买盘数据生成快照")
                asks_items = []
            else:
                # 取卖盘的前50档
                asks_items = sorted(orderbook.asks.items(), key=lambda x: x[0])[:50]
            
            # 进一步放宽条件，如果两边都为空才真正放弃
            if not bids_items and not asks_items:
                logger.warning("买卖盘数据排序后均为空，无法生成快照")
                return
            
            # 转换为列表格式
            bids_data = [[price, quantity] for price, quantity in bids_items]
            asks_data = [[price, quantity] for price, quantity in asks_items]
            
            # 计算统计指标 (放宽条件，允许一边为空)
            best_bid_price = bids_items[0][0] if bids_items else 0
            best_ask_price = asks_items[0][0] if asks_items else 0
            
            # 如果两个价格都为0，则无法计算价差，放弃快照
            if best_bid_price <= 0 and best_ask_price <= 0:
                logger.warning("买卖盘价格均无效，无法生成快照 - 最佳买价：0, 最佳卖价：0")
                return
                
            # 如果只有一边有有效价格，使用该价格作为参考
            if best_bid_price <= 0 and best_ask_price > 0:
                best_bid_price = best_ask_price * 0.99  # 估计买价为卖价的99%
                logger.warning(f"买盘价格无效，使用估计值: {best_bid_price}")
            elif best_ask_price <= 0 and best_bid_price > 0:
                best_ask_price = best_bid_price * 1.01  # 估计卖价为买价的101%
                logger.warning(f"卖盘价格无效，使用估计值: {best_ask_price}")
            
            bid_volume = sum(quantity for _, quantity in bids_items) if bids_items else 0
            ask_volume = sum(quantity for _, quantity in asks_items) if asks_items else 0
            
            spread = best_ask_price - best_bid_price
            spread_percent = (spread / best_bid_price * 100) if best_bid_price else 0
            
            # 创建快照
            # 使用带毫秒的时间戳
            timestamp = datetime.now()
            # 确保毫秒精度
            timestamp_ms = int(time.time() * 1000)  # 毫秒级时间戳
            
            snapshot = {
                'symbol': orderbook.symbol,
                'timestamp': timestamp,
                'timestamp_ms': timestamp_ms,  # 添加毫秒级时间戳
                'update_id': orderbook.last_update_id,
                'bids_data': bids_data,
                'asks_data': asks_data,
                'best_bid_price': best_bid_price,
                'best_ask_price': best_ask_price,
                'bid_volume': bid_volume,
                'ask_volume': ask_volume,
                'spread': spread,
                'spread_percent': spread_percent
            }
            
            depth_snapshots.append(snapshot)
            
            # 打印深度信息（使用毫秒级时间戳）
            logger.info(
                f"深度快照已生成: {orderbook.symbol} @ {timestamp.strftime('%H:%M:%S.%f')[:-3]} [时间戳:{timestamp_ms}], " 
                f"买盘最高价: {best_bid_price}, 卖盘最低价: {best_ask_price}, " 
                f"买盘总量: {bid_volume:.2f}, 卖盘总量: {ask_volume:.2f}, " 
                f"点差: {spread_percent:.4f}%, 当前深度快照数量: {len(depth_snapshots)}"
            )
    except Exception as e:
        logger.error(f"生成深度快照失败: {e}", exc_info=True)

def schedule_snapshots():
    """
    设置定时任务，每500毫秒生成一次订单簿快照
    """
    global is_running, depth_snapshots
    
    logger.info("开始调度快照生成任务，每500毫秒生成一次订单簿快照")
    
    # 确保depth_snapshots已初始化
    if depth_snapshots is None:
        depth_snapshots = []
        logger.info("初始化depth_snapshots列表")
    
    # 首次运行先尝试生成一次快照
    take_snapshot()
    
    snapshot_count = 0
    last_log_time = time.time()
    last_snapshot_time = time.time()
    
    # 循环检查并运行快照生成
    while is_running:
        current_time = time.time()
        
        # 每500毫秒生成一次快照
        if current_time - last_snapshot_time >= 0.5:  # 500毫秒 = 0.5秒
            take_snapshot()
            last_snapshot_time = current_time
            snapshot_count += 1
            
        # 每30秒记录一次快照调度状态
        if current_time - last_log_time > 30:
            with lock:
                snapshot_count_now = len(depth_snapshots)
            
            new_snapshots = snapshot_count
            logger.info(f"快照调度状态：过去30秒新增了 {new_snapshots} 个快照，当前总计 {snapshot_count_now} 个快照")
            snapshot_count = 0
            last_log_time = current_time
            
            # 如果长时间没有新快照，尝试强制生成一次
            if new_snapshots == 0:
                logger.warning("30秒内没有新快照生成，尝试强制生成一次")
                take_snapshot()
            
        time.sleep(0.05)  # 短暂休眠，减少CPU使用

def retry_initialize_orderbook(max_retries=5, initial_retry_delay=2):
    """
    尝试初始化订单簿，失败时进行指数退避重试
    
    参数:
        max_retries: 最大重试次数
        initial_retry_delay: 初始重试延迟（秒）
    """
    for attempt in range(1, max_retries + 1):
        # 使用指数退避算法计算当前尝试的等待时间
        retry_delay = initial_retry_delay * (2 ** (attempt - 1))
        
        logger.info(f"尝试初始化订单簿 (第 {attempt}/{max_retries} 次)")
        
        # 尝试执行初始化
        result = initialize_orderbook()
        if result:
            logger.info("订单簿初始化成功")
            return True
        
        # 如果是最后一次尝试，就不再等待
        if attempt >= max_retries:
            logger.error(f"订单簿初始化失败，已达到最大重试次数 {max_retries}")
            break
            
        # 否则等待指数增长的时间后重试
        logger.warning(f"订单簿初始化失败，{retry_delay}秒后重试...")
        time.sleep(retry_delay)
    
    logger.error("所有初始化尝试均失败，可能需要检查API连接或限制")
    return False

def initialize_orderbook():
    """
    初始化订单簿
    
    根据币安文档要求实现本地订单簿的初始化:
    1. 获取深度快照
    2. 丢弃缓存中u小于快照lastUpdateId的更新
    3. 从第一个U<=lastUpdateId+1且u>=lastUpdateId+1的更新开始应用
    4. 检查pu是否等于之前的u来验证连续性
    
    返回:
        bool: 是否成功初始化订单簿
    """
    global orderbook, depth_cache, SYMBOL
    
    try:
        # 获取当前使用的交易对
        current_symbol = SYMBOL
        
        # 首先尝试从上下文状态恢复订单簿
        logger.info(f"尝试从上下文状态恢复 {current_symbol.upper()} 订单簿")
        state_data = get_latest_context_state(current_symbol)
        
        if state_data:
            # 尝试恢复订单簿
            if orderbook.restore_from_state(state_data):
                logger.info(f"成功从上下文状态恢复 {current_symbol.upper()} 订单簿，跳过REST API初始化")
                return True
            else:
                logger.warning(f"从上下文状态恢复失败，将使用REST API初始化订单簿")
        else:
            logger.info(f"未找到 {current_symbol.upper()} 的上下文状态，将使用REST API初始化订单簿")
        
        # 检查是否可以调用REST API
        if not can_call_rest_api():
            wait_time = REST_CALL_INTERVAL - (time.time() - last_rest_call_time)
            logger.warning(f"REST API调用过于频繁，需等待 {wait_time:.1f} 秒")
            return False
            
        # 获取深度快照
        snapshot_url = f"{BINANCE_REST}/fapi/v1/depth?symbol={current_symbol.upper()}&limit=1000"
        
        logger.info(f"正在从Binance获取 {current_symbol.upper()} 订单簿快照...")
        response = requests.get(snapshot_url)
        
        if response.status_code != 200:
            logger.error(f"获取订单簿快照失败: HTTP {response.status_code} - {response.text}")
            return False
            
        snapshot = response.json()
        last_update_id = snapshot.get('lastUpdateId')
        
        logger.info(f"获取到 {current_symbol.upper()} 订单簿快照，最后更新ID: {last_update_id}")
        
        # 保存初始盘口数据到数据库
        save_initial_orderbook(snapshot)
        
        # 再次检查交易对是否已切换
        if current_symbol != SYMBOL:
            logger.warning(f"交易对已在获取快照期间切换 {current_symbol.upper()} -> {SYMBOL.upper()}，放弃当前操作")
            return False
        
        with lock:
            # 记录缓存状态
            cache_size = len(depth_cache)
            logger.info(f"当前深度缓存大小: {cache_size} 条")
            
            if cache_size > 0:
                # 打印缓存中的第一个和最后一个更新的信息
                first_cache = depth_cache[0]
                last_cache = depth_cache[-1]
                logger.info(f"缓存范围: 第一条 U={first_cache.get('U')} u={first_cache.get('u')}, 最后一条 U={last_cache.get('U')} u={last_cache.get('u')}")
            
            # 重置订单簿
            orderbook.bids = {}
            orderbook.asks = {}
            orderbook.last_update_id = last_update_id
            
            # 处理快照数据
            for bid in snapshot.get('bids', []):
                price = float(bid[0])
                size = float(bid[1])
                if size > 0:
                    orderbook.bids[price] = size
                    
            for ask in snapshot.get('asks', []):
                price = float(ask[0])
                size = float(ask[1])
                if size > 0:
                    orderbook.asks[price] = size
            
            logger.info(f"已加载 {current_symbol.upper()} 订单簿快照数据，买单: {len(orderbook.bids)}档，卖单: {len(orderbook.asks)}档")
            
            # 处理缓存的更新
            valid_updates = []
            for update in depth_cache:
                # 按币安文档要求: 只保留u大于快照lastUpdateId的更新
                if update['u'] > last_update_id:
                    valid_updates.append(update)
                else:
                    logger.debug(f"丢弃过期更新: u={update['u']} <= lastUpdateId={last_update_id}")
            
            # 按更新ID排序
            valid_updates.sort(key=lambda x: x['U'])
            
            logger.info(f"过滤后的有效更新数量: {len(valid_updates)}/{cache_size}")
            
            # 寻找第一个满足条件的更新
            found_first_valid = False
            for i, update in enumerate(valid_updates):
                # 按币安文档要求: 寻找第一个U<=lastUpdateId+1且u>=lastUpdateId+1的更新
                if update['U'] <= last_update_id + 1 and update['u'] >= last_update_id + 1:
                    logger.info(f"找到符合条件的首个更新: U={update['U']} (<=lastUpdateId+1), u={update['u']} (>=lastUpdateId+1)")
                    
                    # 应用这个更新
                    apply_depth_update(update)
                    
                    # 保存深度更新到数据库
                    save_depth_update(update)
                    
                    # 然后尝试应用其后的所有更新，确保序列连续性
                    prev_final_id = update['u']
                    continuous = True
                    
                    # 应用后续的更新，确保连续性
                    for next_update in valid_updates[i+1:]:
                        # 验证更新的连续性
                        if next_update['pu'] != prev_final_id:
                            logger.warning(f"检测到后续更新不连续: 期望prev_id={prev_final_id}, 实际pu={next_update['pu']}")
                            continuous = False
                            break
                        
                        # 应用这个更新
                        apply_depth_update(next_update)
                        
                        # 保存到数据库
                        save_depth_update(next_update)
                        
                        # 更新前一个final_id
                        prev_final_id = next_update['u']
                    
                    orderbook.initialized = True
                    found_first_valid = True
                    
                    if continuous:
                        logger.info(f"成功应用缓存的所有连续更新，{current_symbol.upper()} 订单簿初始化完成")
                    else:
                        logger.warning(f"应用了部分缓存更新，但序列不完全连续，{current_symbol.upper()} 订单簿可能需要重新初始化")
                    
                    break
            
            # 清空深度缓存，已无需使用
            depth_cache = []
            
            if found_first_valid:
                logger.info(f"{current_symbol.upper()} 订单簿已完全初始化，缓存已清空")
                # 初始化完成后，保存当前状态到Context表
                update_context_state()
                return True
                
            # 如果未找到理想的更新，但有买卖盘数据，也视为初始化成功
            if len(orderbook.bids) > 0 and len(orderbook.asks) > 0:
                logger.warning(f"未找到有效的深度更新来初始化 {current_symbol.upper()} 订单簿，等待更多数据")
                
                if len(valid_updates) > 0:
                    first_valid = valid_updates[0]
                    logger.warning(f"首个有效更新: U={first_valid['U']}, u={first_valid['u']}, lastUpdateId+1={last_update_id+1}")
                
                # 使用快照数据初始化订单簿
                orderbook.initialized = True
                logger.info(f"使用快照数据初始化 {current_symbol.upper()} 订单簿，等待后续更新")
                # 初始化完成后，保存当前状态到Context表
                update_context_state()
                return True
            else:
                logger.error(f"无法初始化 {current_symbol.upper()} 订单簿: 无买卖盘数据且无有效更新")
                return False
            
    except Exception as e:
        logger.error(f"初始化订单簿失败: {e}", exc_info=True)
        return False

def apply_depth_update(update):
    """
    应用深度更新到订单簿
    
    按照币安文档要求:
    1. 更新数据代表价格绝对水平，不是相对变化
    2. 如果价格对应的数量为0，则删除该价格
    
    参数:
        update: WebSocket接收到的深度更新事件
    """
    global orderbook
    
    try:
        # 获取更新信息
        symbol = update.get('s', '').lower()
        bid_updates = update.get('b', [])
        ask_updates = update.get('a', [])
        
        bid_count = len(bid_updates)
        ask_count = len(ask_updates)
        
        logger.debug(f"应用 {symbol} 深度更新: 买单 {bid_count} 条, 卖单 {ask_count} 条")
        
        if bid_count == 0 and ask_count == 0:
            logger.warning(f"收到空的深度更新：无买单和卖单更新")
            return
        
        bid_changed = 0
        ask_changed = 0
        
        # 更新买盘
        for bid in bid_updates:
            if len(bid) < 2:
                logger.warning(f"买盘更新数据格式错误: {bid}")
                continue
                
            try:
                price = float(bid[0])
                quantity = float(bid[1])
                
                if price <= 0:
                    logger.warning(f"买盘价格无效: {price}")
                    continue
                
                if quantity == 0:
                    # 移除价格水平
                    if price in orderbook.bids:
                        orderbook.bids.pop(price, None)
                        bid_changed += 1
                        logger.debug(f"删除买单价格: {price}")
                else:
                    # 更新或添加价格水平
                    orderbook.bids[price] = quantity
                    bid_changed += 1
            except (ValueError, TypeError) as e:
                logger.warning(f"处理买盘更新时出错: {e}, 数据: {bid}")
                
        # 更新卖盘
        for ask in ask_updates:
            if len(ask) < 2:
                logger.warning(f"卖盘更新数据格式错误: {ask}")
                continue
                
            try:
                price = float(ask[0])
                quantity = float(ask[1])
                
                if price <= 0:
                    logger.warning(f"卖盘价格无效: {price}")
                    continue
                
                if quantity == 0:
                    # 移除价格水平
                    if price in orderbook.asks:
                        orderbook.asks.pop(price, None)
                        ask_changed += 1
                        logger.debug(f"删除卖单价格: {price}")
                else:
                    # 更新或添加价格水平
                    orderbook.asks[price] = quantity
                    ask_changed += 1
            except (ValueError, TypeError) as e:
                logger.warning(f"处理卖盘更新时出错: {e}, 数据: {ask}")
            
        # 更新ID
        old_update_id = orderbook.last_update_id
        orderbook.prev_update_id = old_update_id
        orderbook.last_update_id = update['u']
        
        # 记录每次更新的详细信息
        if bid_changed > 0 or ask_changed > 0:
            logger.debug(f"订单簿已更新: 买盘变更 {bid_changed}/{bid_count}, 卖盘变更 {ask_changed}/{ask_count}, 买盘总数 {len(orderbook.bids)}, 卖盘总数 {len(orderbook.asks)}")
        
        # 如果是重大更新，记录详情
        if bid_count > 50 or ask_count > 50:
            logger.info(f"大量深度更新: {symbol} 有 {bid_count} 个买单和 {ask_count} 个卖单更新, ID从 {old_update_id} 更新到 {update['u']}")
    
    except Exception as e:
        logger.error(f"应用深度更新时发生错误: {e}", exc_info=True)

def can_call_rest_api():
    """
    检查是否可以调用REST API，考虑币安API的速率限制
    
    返回:
        bool: 是否可以调用REST API
    """
    global last_rest_call_time
    current_time = time.time()
    time_since_last_call = current_time - last_rest_call_time
    
    if time_since_last_call >= REST_CALL_INTERVAL:
        last_rest_call_time = current_time
        logger.debug(f"允许REST API调用，距离上次调用已经过去 {time_since_last_call:.2f} 秒")
        return True
    else:
        remaining_time = REST_CALL_INTERVAL - time_since_last_call
        logger.debug(f"REST API调用过于频繁，还需等待 {remaining_time:.2f} 秒")
        return False

def on_message(ws, message):
    """
    处理WebSocket消息
    """
    global orderbook, depth_cache
    
    try:
        data = json.loads(message)
        
        # 处理心跳消息
        if 'ping' in data:
            ws.send(json.dumps({"pong": data['ping']}))
            logger.debug("收到ping请求，已响应pong")
            return
        # print(data)
        
        # 处理聚合交易消息
        if 'e' in data and data['e'] == 'aggTrade':
            logger.info(f"收到聚合交易: {data['s']}, 价格: {data['p']}, 数量: {data['q']}, 时间: {data['T']}")
            # 保存聚合交易数据
            save_agg_trade(data)
            return
            
        # 获取data的时间戳和当前系统时间的差值
        if 'T' in data:
            timestamp = data['T']
            timestamp = datetime.fromtimestamp(timestamp / 1000)
            current_time = datetime.now()  # 当前时间戳（毫秒）
            time_diff = current_time - timestamp
            # 打印时间戳和差值
            logger.info(f"收到消息时间戳: {timestamp}, 当前时间戳: {current_time}, 时间差: {time_diff}")
        
        # 判断是否为深度更新事件
        if 'e' not in data or data['e'] != 'depthUpdate':
            logger.debug(f"收到非深度更新消息: {message[:100]}")
            return
        
        # 更详细的深度更新日志
        symbol = data.get('s', '').lower()
        first_update_id = data.get('U', 0)  # 起始更新ID
        final_update_id = data.get('u', 0)  # 最终更新ID
        prev_final_id = data.get('pu', 0)   # 前一个最终更新ID
        
        logger.debug(f"收到 {symbol} 深度更新: first_id={first_update_id}, final_id={final_update_id}, prev_final_id={prev_final_id}")
        
        # 保存深度更新到数据库
        save_depth_update(data)
        
        # 无论订单簿是否初始化，都直接应用深度更新到orderbook
        # 这样可以确保即使在初始化过程中，orderbook也有最新数据供take_snapshot使用
        apply_depth_update(data)
        logger.debug(f"已应用深度更新到订单簿: 买盘 {len(orderbook.bids)}档, 卖盘 {len(orderbook.asks)}档")
            
        # 如果订单簿未初始化,缓存更新用于正式初始化过程
        if not orderbook.initialized:
            # 将深度更新添加到缓存
            depth_cache.append(data)
            logger.debug(f"订单簿未初始化，将深度更新添加到缓存 (缓存大小: {len(depth_cache)})")
            
            # 第一次收到更新时启动初始化过程
            if len(depth_cache) == 1:
                logger.info(f"收到第一个 {symbol} 深度更新 (U={first_update_id}, u={final_update_id})，开始初始化订单簿")
                threading.Thread(target=retry_initialize_orderbook, daemon=True).start()
            
            # 缓存过大时截断旧数据，避免内存占用过多
            if len(depth_cache) > 1000:
                logger.warning(f"深度更新缓存过大 ({len(depth_cache)}条)，截断为最新的500条")
                depth_cache = depth_cache[-500:]
                
            return
            
        # 验证更新的连续性
        if data['pu'] != orderbook.last_update_id:
            # 更详细的不连续更新日志
            gap = abs(int(data['pu']) - int(orderbook.last_update_id))
            logger.warning(f"检测到不连续的更新: 期望={orderbook.last_update_id}, 实际={data['pu']}, 差距={gap}")
            logger.warning(f"不连续更新详情: 当前更新范围 U={data['U']} 到 u={data['u']}, 上次更新ID={orderbook.last_update_id}")
            
            # 如果差距很小，可以尝试继续处理
            if 0 < gap <= 5:  # 容忍小的不连续
                logger.info(f"不连续差距较小({gap})，尝试继续处理而不重新初始化")
                # 这里不需要再次应用更新，因为前面已经应用过了
                return
                
            # 差距较大，需要重新初始化
            orderbook.initialized = False
            depth_cache = [data]  # 保留当前更新
            # logger.warning(f"由于不连续更新(差距={gap})，重新初始化订单簿")
            # threading.Thread(target=retry_initialize_orderbook, daemon=True).start()
            return
            
        # 由于前面已经应用了更新，这里不需要重复操作
        logger.debug(f"深度更新已成功应用: {symbol} U={first_update_id}, u={final_update_id}")
        
    except Exception as e:
        logger.error(f"处理WebSocket消息异常: {e}", exc_info=True)


def on_ping(ws, message):
    """
    WebSocket Ping回调函数

    参数:
        ws: WebSocket连接对象
        message: Ping消息内容 (字节类型)
    """
    global ping_counter, last_ping_time
    logger.info(f"收到ping消息{message}")
    current_time = time.time()
    if current_time - last_ping_time >= 1:
        ping_counter = 1
        last_ping_time = current_time
    else:
        ping_counter += 1

    if ping_counter <= 5:  # 遵循每秒最多5次的限制
        try:
            # 直接发送pong帧，保持相同的payload
            ws.sock.pong(message)
            logger.debug("回应ping")
        except Exception as e:
            logger.error(f"回应ping时出错: {e}")


def on_pong(ws, message):
    """
    WebSocket Pong回调函数

    参数:
        ws: WebSocket连接对象
        message: Pong消息内容
    """
    logger.debug("收到Pong消息")


def on_error(ws, error):
    """
    WebSocket错误回调
    
    参数:
        ws: WebSocket连接对象
        error: 错误信息
    """
    global reconnect_count, ws_connected
    
    logger.error(f"WebSocket连接错误: {error}")
    ws_connected = False
    
    # 增加重连计数
    reconnect_count += 1

def on_close(ws, close_status_code, close_msg):
    """
    WebSocket连接关闭回调
    
    参数:
        ws: WebSocket连接对象
        close_status_code: 关闭状态码
        close_msg: 关闭消息
    """
    global reconnect_count, orderbook, ws_connected
    
    logger.warning(f"WebSocket连接关闭: {close_status_code} {close_msg}")
    ws_connected = False
    
    # 重置订单簿状态
    orderbook.initialized = False
    reconnect_websocket()

def on_open(ws):
    """
    WebSocket连接建立回调
    
    参数:
        ws: WebSocket连接对象
    """
    global reconnect_count, ws_connected
    
    logger.info(f"已连接到Binance WebSocket，准备订阅 {SYMBOL.upper()} 数据")
    ws_connected = True
    
    # 重置重连计数
    reconnect_count = 0
    
    # 订阅深度更新和聚合交易
    subscription = {
        "method": "SUBSCRIBE",
        "params": [
            f"{SYMBOL}@depth@100ms",  # 深度更新，100ms
            f"{SYMBOL}@aggTrade"      # 聚合交易
        ],
        "id": 1
    }
    
    try:
        ws.send(json.dumps(subscription))
        logger.info(f"已发送 {SYMBOL.upper()} 订阅请求: 深度更新和聚合交易")
    except Exception as e:
        logger.error(f"发送订阅请求失败: {e}")
    
    # 在单独的线程中初始化订单簿，并添加重试机制
    threading.Thread(target=retry_initialize_orderbook, daemon=True).start()

def websocket_thread_func():
    """
    WebSocket线程函数，负责维护WebSocket连接
    """
    global ws, reconnect_count, SYMBOL
    
    while is_running:
        try:
            # 获取当前监控的交易对（可能已被其他线程修改）
            current_symbol = SYMBOL
            
            logger.info(f"正在创建WebSocket连接，准备订阅 {current_symbol.upper()}")
            
            # 创建WebSocket连接
            ws = websocket.WebSocketApp(
                BINANCE_WSS,
                on_open=on_open,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close,
                on_ping=on_ping,
                on_pong=on_pong
            )
            
            # 运行WebSocket连接，直到连接关闭
            ws.run_forever(ping_interval=60, ping_timeout=10)
            
            # 等待重连事件
            if is_running and reconnect_count < max_reconnect_attempts:
                logger.info("等待重连事件...")
                reconnect_event.wait(timeout=60)  # 最多等待60秒
                
                if reconnect_event.is_set():
                    # 检查交易对是否已更改
                    if current_symbol != SYMBOL:
                        logger.info(f"检测到交易对已更改 {current_symbol.upper()} -> {SYMBOL.upper()}，准备使用新交易对重连")
                    else:
                        logger.info("收到重连事件，准备重新连接...")
                    reconnect_event.clear()
                else:
                    logger.warning("重连事件等待超时，强制重连...")
            else:
                # 如果不再运行或达到最大重连次数，退出线程
                break
                
        except Exception as e:
            logger.error(f"WebSocket线程异常: {e}", exc_info=True)
            time.sleep(5)  # 出现异常时等待一段时间再重试
    
    logger.info("WebSocket线程结束")

def connect_websocket():
    """
    启动WebSocket连接线程
    
    返回:
        threading.Thread: WebSocket运行线程
    """
    global ws_thread
    
    # 如果已有线程在运行，先停止它
    if ws_thread and ws_thread.is_alive():
        logger.info("已有WebSocket线程在运行，不再创建新线程")
        return ws_thread
    
    # 创建并启动新的WebSocket线程
    ws_thread = threading.Thread(target=websocket_thread_func)
    ws_thread.daemon = True  # 设置为守护线程，主线程退出时自动结束
    ws_thread.start()
    
    logger.info("WebSocket线程已启动")
    return ws_thread

def reconnect_websocket():
    """
    触发WebSocket重新连接
    """
    global ws
    logger.info("触发WebSocket重连...")
    
    # 关闭现有连接，会触发on_close回调
    if ws:
        try:
            ws.close()
        except Exception as e:
            logger.error(f"关闭WebSocket连接异常: {e}")
    
    # 设置重连事件，通知线程进行重连
    reconnect_event.set()

def check_connection_status():
    """
    检查WebSocket连接状态
    
    返回:
        bool: 连接是否正常
    """
    global ws, ws_connected
    
    # 直接返回连接状态标志
    return ws_connected

def monitor_connection():
    """
    监控WebSocket连接状态
    
    每30秒检查一次连接状态，如果连接断开则尝试重新连接
    """
    global is_running
    
    logger.info("启动WebSocket连接监控")
    
    while is_running:
        # 每30秒检查一次连接状态
        time.sleep(30)
        
        # 检查连接是否正常
        if not check_connection_status() and is_running:
            logger.warning("检测到WebSocket连接已断开，尝试重新连接...")
            # 重新连接
            reconnect_websocket()

def signal_handler(sig, frame):
    """
    信号处理函数
    
    处理SIGINT和SIGTERM信号，优雅地关闭程序
    
    参数:
        sig: 信号编号
        frame: 当前栈帧
    """
    global is_running
    
    logger.info(f"收到信号 {sig}，准备关闭程序...")
    is_running = False
    
    # 关闭WebSocket连接
    if ws:
        ws.close()
    
    # 保存最后的数据
    save_to_mariadb()
    
    # 保存最终的Context状态
    if orderbook.initialized:
        logger.info("保存最终的订单簿状态...")
        update_context_state()
    
    logger.info("程序已关闭")
    sys.exit(0)

def check_and_switch_symbol():
    """
    检查是否需要切换监控的交易对，每隔2小时评估一次
    """
    global SYMBOL, last_symbol_change_time, depth_snapshots, orderbook
    
    current_time = time.time()
    
    # 如果距离上次切换不足2小时，则不进行切换
    if current_time - last_symbol_change_time < SYMBOL_CHANGE_INTERVAL:
        return False
    
    with symbol_change_lock:
        # 双重检查，避免多线程问题
        if current_time - last_symbol_change_time < SYMBOL_CHANGE_INTERVAL:
            return False
            
        logger.info(f"开始评估交易对，当前监控: {SYMBOL.upper()}")
        
        # 查找波动最大的交易对
        new_symbol = find_most_volatile_symbol(get_symbol_24h_stats([SYMBOL]))
        
        # 如果找不到新的交易对或新旧相同，则继续使用当前的
        if not new_symbol or new_symbol == SYMBOL:
            if not new_symbol:
                logger.warning("无法确定波动最大的交易对，继续使用当前交易对")
            else:
                logger.info(f"当前交易对 {SYMBOL.upper()} 仍然是波动最大的，继续监控")
                
            # 即使没有切换交易对，也更新时间戳
            last_symbol_change_time = current_time
            return False
        
        logger.info(f"切换监控交易对: {SYMBOL.upper()} -> {new_symbol.upper()}")
        
        # 保存当前积累的数据
        if depth_snapshots:
            save_to_mariadb()
        
        # 更新全局变量
        old_symbol = SYMBOL
        SYMBOL = new_symbol
        
        # 重置订单簿
        orderbook = OrderBook(SYMBOL)
        
        # 清空深度缓存
        with lock:
            depth_snapshots = []
            depth_cache = []
        
        # 更新时间戳
        last_symbol_change_time = current_time
        
        # 触发WebSocket重连，使用新交易对
        reconnect_websocket()
        
        logger.info(f"已切换监控交易对: {old_symbol.upper()} -> {SYMBOL.upper()}")
        return True

def setup_logger():
    """
    设置日志配置
    """
    global logger
    # 日志已经在文件开头配置好了，这里只需返回记录器
    return logger

def create_db_connection():
    """
    创建数据库连接
    
    返回:
        connection: 数据库连接对象
    """
    try:
        if mariadb_pool is None:
            logger.error("MariaDB连接池不可用，无法创建连接")
            return None
            
        # 从连接池获取连接
        conn = mariadb_pool.get_connection()
        logger.info("已成功创建数据库连接")
        return conn
    except Exception as e:
        logger.error(f"创建数据库连接失败: {e}", exc_info=True)
        return None

def db_thread_func(db_conn):
    """
    数据库处理线程函数
    
    负责定期将缓存的订单簿快照数据保存到数据库
    
    参数:
        db_conn: 数据库连接对象
    """
    global is_running
    
    logger.info("数据库处理线程已启动")
    
    # 设置定时保存任务
    schedule.every(20).seconds.do(save_to_mariadb)
    
    # 立即执行一次，确保功能正常
    logger.info("数据库线程初始化时尝试立即保存一次")
    save_to_mariadb()
    
    save_count = 0
    last_log_time = time.time()
    
    while is_running:
        schedule.run_pending()
        
        # 每60秒记录一次保存状态
        current_time = time.time()
        if current_time - last_log_time > 60:
            logger.info(f"数据库处理状态：已执行 {save_count} 次保存操作")
            last_log_time = current_time
            
        # 如果快照数量超过500，立即触发保存
        if len(depth_snapshots) > 500:
            logger.warning(f"快照数量({len(depth_snapshots)})超过500，立即触发保存")
            save_to_mariadb()
            save_count += 1
            
        time.sleep(1)
    
    # 程序结束前确保保存所有数据
    if len(depth_snapshots) > 0:
        logger.info(f"程序结束前保存剩余 {len(depth_snapshots)} 条快照")
        save_to_mariadb()
    
    logger.info("数据库处理线程已结束")

def get_latest_orderbook_data(symbol=None, limit=20):
    """
    从数据库获取最新的订单簿数据
    
    参数:
        symbol (str): 交易对名称，如不指定则获取所有交易对
        limit (int): 返回记录条数，默认20条
    
    返回:
        DataFrame: 包含订单簿数据的DataFrame
    """
    try:
        if mariadb_pool is None:
            logger.error("MariaDB连接池不可用，无法获取数据")
            return None
            
        # 获取连接
        conn = mariadb_pool.get_connection()
        cursor = conn.cursor(dictionary=True)  # 返回字典格式结果
        
        # 准备SQL查询
        if symbol:
            sql = f"""
            SELECT 
                id, symbol, timestamp, timestamp_ms, update_id, 
                bids_json, asks_json, 
                best_bid_price, best_ask_price, 
                bid_volume, ask_volume, 
                spread, spread_percent, created_at
            FROM 
                {MARIADB_TABLE} 
            WHERE 
                symbol = %s
            ORDER BY 
                timestamp_ms DESC 
            LIMIT %s
            """
            cursor.execute(sql, (symbol.lower(), limit))
        else:
            sql = f"""
            SELECT 
                id, symbol, timestamp, timestamp_ms, update_id, 
                bids_json, asks_json, 
                best_bid_price, best_ask_price, 
                bid_volume, ask_volume, 
                spread, spread_percent, created_at
            FROM 
                {MARIADB_TABLE} 
            ORDER BY 
                timestamp_ms DESC 
            LIMIT %s
            """
            cursor.execute(sql, (limit,))
        
        # 获取结果
        records = cursor.fetchall()
        
        # 关闭数据库连接
        cursor.close()
        conn.close()
        
        if not records:
            logger.warning(f"未找到符合条件的订单簿数据：symbol={symbol}, limit={limit}")
            return None
        
        # 转换为DataFrame
        import pandas as pd
        df = pd.DataFrame(records)
        
        # 解析JSON字符串为Python对象
        if 'bids_json' in df.columns:
            df['bids'] = df['bids_json'].apply(json.loads)
        if 'asks_json' in df.columns:
            df['asks'] = df['asks_json'].apply(json.loads)
        
        # 添加额外的计算列
        df['mid_price'] = (df['best_bid_price'] + df['best_ask_price']) / 2
        df['imbalance'] = (df['bid_volume'] - df['ask_volume']) / (df['bid_volume'] + df['ask_volume'])
        
        logger.info(f"成功从数据库获取了 {len(df)} 条订单簿数据")
        
        return df
        
    except Exception as e:
        logger.error(f"获取订单簿数据失败: {e}", exc_info=True)
        return None

def visualize_orderbook_data(df, top_n=5):
    """
    可视化订单簿数据
    
    参数:
        df (DataFrame): 包含订单簿数据的DataFrame
        top_n (int): 显示买卖盘的前几档，默认5档
    
    返回:
        None: 直接显示图表
    """
    try:
        if df is None or len(df) == 0:
            logger.error("没有可视化的数据")
            return
            
        # 选择最新的一条记录
        latest_record = df.iloc[0]
        symbol = latest_record['symbol'].upper()
        timestamp = latest_record['timestamp']
        timestamp_ms = latest_record['timestamp_ms']
        
        # 获取买卖盘数据
        bids = latest_record['bids'][:top_n]
        asks = latest_record['asks'][:top_n]
        
        # 提取价格和数量
        bid_prices = [float(bid[0]) for bid in bids]
        bid_quantities = [float(bid[1]) for bid in bids]
        ask_prices = [float(ask[0]) for ask in asks]
        ask_quantities = [float(ask[1]) for ask in asks]
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # 设置图表标题，包含毫秒级时间戳
        fig.suptitle(f'{symbol} 订单簿 @ {timestamp} [时间戳:{timestamp_ms}]', fontsize=16)
        
        # 买盘柱状图（绿色）
        ax1.barh(range(len(bid_prices)), bid_quantities, color='green', alpha=0.6)
        ax1.set_yticks(range(len(bid_prices)))
        ax1.set_yticklabels([f'{price:.4f}' for price in bid_prices])
        ax1.set_title('买盘', fontsize=14)
        ax1.set_xlabel('数量', fontsize=12)
        ax1.set_ylabel('价格', fontsize=12)
        
        # 卖盘柱状图（红色）
        ax2.barh(range(len(ask_prices)), ask_quantities, color='red', alpha=0.6)
        ax2.set_yticks(range(len(ask_prices)))
        ax2.set_yticklabels([f'{price:.4f}' for price in ask_prices])
        ax2.set_title('卖盘', fontsize=14)
        ax2.set_xlabel('数量', fontsize=12)
        
        # 添加网格
        ax1.grid(True, alpha=0.3)
        ax2.grid(True, alpha=0.3)
        
        # 添加买卖盘深度信息
        bid_depth = latest_record['bid_volume']
        ask_depth = latest_record['ask_volume']
        spread = latest_record['spread']
        spread_percent = latest_record['spread_percent']
        
        fig.text(0.5, 0.01, 
                f'买盘深度: {bid_depth:.2f} | 卖盘深度: {ask_depth:.2f} | ' 
                f'点差: {spread:.6f} ({spread_percent:.4f}%)', 
                ha='center', va='center', fontsize=12)
        
        plt.tight_layout()
        plt.subplots_adjust(bottom=0.15)
        
        # 显示图表
        plt.show()
        
        logger.info(f"已生成 {symbol} 订单簿可视化图表")
        
        # 绘制时间序列图表（如果有多条记录）
        if len(df) > 1:
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))
            
            # 时间格式化 - 使用毫秒级时间戳创建更精确的时间索引
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            # 根据毫秒时间戳创建一个新的时间列，更精确
            df['ms_time'] = pd.to_datetime(df['timestamp_ms'], unit='ms')
            df = df.sort_values('ms_time')
            
            # 使用毫秒级时间轴绘图
            # 中间价格走势
            ax1.plot(df['ms_time'], df['mid_price'], 'b-', linewidth=1.5)
            ax1.set_title(f'{symbol} 中间价格走势 (毫秒精度)', fontsize=14)
            ax1.grid(True, alpha=0.3)
            
            # 买卖盘深度
            ax2.plot(df['ms_time'], df['bid_volume'], 'g-', label='买盘深度')
            ax2.plot(df['ms_time'], df['ask_volume'], 'r-', label='卖盘深度')
            ax2.set_title('买卖盘深度走势', fontsize=14)
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 买卖不平衡度
            ax3.plot(df['ms_time'], df['imbalance'], 'purple', linewidth=1.5)
            ax3.axhline(y=0, color='k', linestyle='--', alpha=0.3)
            ax3.set_title('买卖不平衡度 (正值表示买盘占优)', fontsize=14)
            ax3.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.show()
            
            logger.info(f"已生成 {symbol} 订单簿时间序列图表")
            
    except Exception as e:
        logger.error(f"可视化订单簿数据失败: {e}", exc_info=True)

def main():
    global SYMBOL, ws_connected
    
    # 设置日志
    setup_logger()
    logger.info("开始执行订单簿更新程序")
    
    # 创建数据库连接
    db_conn = create_db_connection()
    init_mariadb()
    if not db_conn:
        logger.error("无法连接到数据库，程序终止")
        return
        
    # 初始化变量
    global orderbook, cached_depth_updates, depth_snapshots
    # 重置OrderBook对象
    orderbook = OrderBook(SYMBOL)
    cached_depth_updates = []
    depth_snapshots = []
    
    try:
        # 初始化信号处理
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 1. 先获取最具波动性的交易对
        logger.info("准备获取最具波动性的交易对")
        # 确保不会触发API频率限制
        time.sleep(1)
        
        # 获取所有合约交易对
        all_symbols = get_all_futures_symbols()
        if not all_symbols:
            logger.warning("无法获取交易对列表，使用默认交易对SOLUSDT")
            SYMBOL = "solusdt"
        else:
            # 由于可能有API调用频率限制，添加额外等待时间
            logger.info("成功获取交易对列表，等待2秒后获取24小时统计...")
            time.sleep(2)
            
            # 获取交易对24小时统计
            symbol_stats = get_symbol_24h_stats(all_symbols)
            if not symbol_stats:
                logger.warning("无法获取交易对24小时统计，使用默认交易对SOLUSDT")
                SYMBOL = "solusdt"
            else:
                # 获取波动性最大的交易对
                most_volatile = find_most_volatile_symbol(symbol_stats)
                if most_volatile:
                    SYMBOL = most_volatile
                    logger.info(f"选择波动性最大的交易对: {SYMBOL.upper()}")
                else:
                    logger.warning("无法确定波动性最大的交易对，使用默认交易对SOLUSDT")
                    SYMBOL = "solusdt"
        
        # 更新程序初始化时间和交易对
        global program_start_time, last_symbol_change_time
        program_start_time = time.time()
        last_symbol_change_time = time.time()
        
        logger.info(f"初始交易对设置为: {SYMBOL.upper()}")
        
        # 2. 现在创建WebSocket线程
        ws_thread = threading.Thread(target=websocket_thread_func)
        ws_thread.daemon = True
        ws_thread.start()
        
        # 3. 创建数据库存储线程
        db_thread = threading.Thread(target=db_thread_func, args=(db_conn,))
        db_thread.daemon = True
        db_thread.start()
        
        # 3.1 创建快照生成线程
        snapshot_thread = threading.Thread(target=schedule_snapshots)
        snapshot_thread.daemon = True
        snapshot_thread.start()
        logger.info("快照生成线程已启动")
        
        # 4. 等待WebSocket连接建立
        retry_count = 0
        max_retries = 5
        while not ws_connected and retry_count < max_retries:
            logger.info(f"等待WebSocket连接建立... (尝试 {retry_count+1}/{max_retries})")
            time.sleep(2)
            retry_count += 1
            
        if not ws_connected:
            logger.error(f"WebSocket连接建立失败，已尝试 {max_retries} 次")
            return
            
        logger.info("WebSocket连接已建立，主线程继续执行")
        
        # 主循环
        try:
            while True:
                # 定期检查是否需要切换交易对
                check_and_switch_symbol()
                
                # 每分钟记录一次状态
                logger.info(f"当前交易对: {SYMBOL.upper()}, 订单簿初始化: {orderbook.initialized}, "
                          f"缓存的深度更新: {len(cached_depth_updates)}, 快照数量: {len(depth_snapshots)}")
                
                time.sleep(60)
        except KeyboardInterrupt:
            logger.info("接收到键盘中断信号，程序将退出")
        
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {e}", exc_info=True)
        
    finally:
        # 关闭数据库连接
        if db_conn:
            try:
                db_conn.close()
                logger.info("数据库连接已关闭")
            except Exception as e:
                logger.error(f"关闭数据库连接时发生错误: {e}")
        
        logger.info("程序执行完毕")
        
if __name__ == "__main__":
    main()
    
# 使用示例：
"""
# 获取并可视化最新的订单簿数据
def analyze_orderbook_data():
    # 获取特定交易对最新的20条数据
    symbol = "btcusdt"  # 可以替换为任何交易对
    df = get_latest_orderbook_data(symbol=symbol, limit=20)
    
    if df is not None and not df.empty:
        # 显示基本统计信息
        print(f"订单簿统计信息:")
        print(f"总记录数: {len(df)}")
        print(f"时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
        print(f"平均中间价格: {df['mid_price'].mean():.4f}")
        print(f"平均买卖深度: 买盘 {df['bid_volume'].mean():.2f}, 卖盘 {df['ask_volume'].mean():.2f}")
        print(f"平均点差百分比: {df['spread_percent'].mean():.4f}%")
        
        # 可视化订单簿数据
        visualize_orderbook_data(df, top_n=10)
    else:
        print(f"未找到 {symbol} 的订单簿数据")

# 要运行分析，取消下面一行的注释
# analyze_orderbook_data()
"""


