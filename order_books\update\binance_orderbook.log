2025-06-27 20:13:55,264 - __main__ - INFO - MariaDB连接池创建成功
2025-06-27 20:13:55,266 - __main__ - INFO - 开始执行订单簿更新程序
2025-06-27 20:13:55,337 - __main__ - INFO - 已成功创建数据库连接
2025-06-27 20:13:56,406 - __main__ - INFO - MariaDB数据库和表已初始化
2025-06-27 20:13:57,004 - __main__ - INFO - 准备获取最具波动性的交易对
2025-06-27 20:13:58,033 - __main__ - INFO - 正在获取币安期货交易对列表 (第 1/3 次尝试)...
2025-06-27 20:13:59,294 - __main__ - INFO - 获取到 476 个交易中的期货交易对
2025-06-27 20:13:59,296 - __main__ - INFO - 成功获取交易对列表，等待2秒后获取24小时统计...
2025-06-27 20:14:01,297 - __main__ - INFO - 正在获取24小时价格统计 (第 1/3 次尝试)...
2025-06-27 20:14:02,702 - __main__ - INFO - 成功获取到 521 个交易对的24小时统计
2025-06-27 20:14:02,704 - __main__ - INFO - 已获取并排序 475 个交易对的24小时统计
2025-06-27 20:14:02,705 - __main__ - INFO - 排名1: SAHARAUSDT - 波动率: 37.24%, 成交量: 2393183260.03 USDT, 波动率得分: 1821738.30
2025-06-27 20:14:02,705 - __main__ - INFO - 排名2: BANANAS31USDT - 波动率: 32.61%, 成交量: 844091425.03 USDT, 波动率得分: 947310.32
2025-06-27 20:14:02,706 - __main__ - INFO - 排名3: BSWUSDT - 波动率: 35.22%, 成交量: 281391858.25 USDT, 波动率得分: 590772.75
2025-06-27 20:14:02,706 - __main__ - INFO - 排名4: HIFIUSDT - 波动率: 37.64%, 成交量: 190091237.48 USDT, 波动率得分: 518942.36
2025-06-27 20:14:02,706 - __main__ - INFO - 排名5: SEIUSDT - 波动率: 9.24%, 成交量: 556277430.82 USDT, 波动率得分: 217953.92
2025-06-27 20:14:02,707 - __main__ - INFO - 排名6: HUSDT - 波动率: 11.87%, 成交量: 259311748.03 USDT, 波动率得分: 191192.81
2025-06-27 20:14:02,707 - __main__ - INFO - 排名7: XRPUSDT - 波动率: 3.51%, 成交量: 1399094223.52 USDT, 波动率得分: 131364.49
2025-06-27 20:14:02,707 - __main__ - INFO - 排名8: MYXUSDT - 波动率: 6.46%, 成交量: 245993209.05 USDT, 波动率得分: 101319.74
2025-06-27 20:14:02,708 - __main__ - INFO - 排名9: FARTCOINUSDT - 波动率: 4.82%, 成交量: 425238525.39 USDT, 波动率得分: 99291.62
2025-06-27 20:14:02,708 - __main__ - INFO - 排名10: RAREUSDT - 波动率: 6.55%, 成交量: 208453030.18 USDT, 波动率得分: 94626.01
2025-06-27 20:14:02,709 - __main__ - INFO - 正在获取币安期货交易对列表 (第 1/3 次尝试)...
2025-06-27 20:14:03,932 - __main__ - INFO - 获取到 476 个交易中的期货交易对
2025-06-27 20:14:03,934 - __main__ - INFO - 正在获取24小时价格统计 (第 1/3 次尝试)...
2025-06-27 20:14:05,311 - __main__ - INFO - 成功获取到 521 个交易对的24小时统计
2025-06-27 20:14:05,313 - __main__ - INFO - 已获取并排序 475 个交易对的24小时统计
2025-06-27 20:14:05,313 - __main__ - INFO - 排名1: SAHARAUSDT - 波动率: 37.24%, 成交量: 2393183260.03 USDT, 波动率得分: 1821738.30
2025-06-27 20:14:05,313 - __main__ - INFO - 排名2: BANANAS31USDT - 波动率: 32.61%, 成交量: 844091425.03 USDT, 波动率得分: 947310.32
2025-06-27 20:14:05,313 - __main__ - INFO - 排名3: BSWUSDT - 波动率: 35.22%, 成交量: 281391858.25 USDT, 波动率得分: 590772.75
2025-06-27 20:14:05,314 - __main__ - INFO - 排名4: HIFIUSDT - 波动率: 37.64%, 成交量: 190091237.48 USDT, 波动率得分: 518942.36
2025-06-27 20:14:05,314 - __main__ - INFO - 排名5: SEIUSDT - 波动率: 9.24%, 成交量: 556277430.82 USDT, 波动率得分: 217953.92
2025-06-27 20:14:05,314 - __main__ - INFO - 排名6: HUSDT - 波动率: 11.87%, 成交量: 259311748.03 USDT, 波动率得分: 191192.81
2025-06-27 20:14:05,314 - __main__ - INFO - 排名7: XRPUSDT - 波动率: 3.51%, 成交量: 1399094223.52 USDT, 波动率得分: 131364.49
2025-06-27 20:14:05,314 - __main__ - INFO - 排名8: MYXUSDT - 波动率: 6.46%, 成交量: 245993209.05 USDT, 波动率得分: 101319.74
2025-06-27 20:14:05,314 - __main__ - INFO - 排名9: FARTCOINUSDT - 波动率: 4.82%, 成交量: 425238525.39 USDT, 波动率得分: 99291.62
2025-06-27 20:14:05,314 - __main__ - INFO - 排名10: RAREUSDT - 波动率: 6.55%, 成交量: 208453030.18 USDT, 波动率得分: 94626.01
2025-06-27 20:14:05,315 - __main__ - INFO - 当前波动最大的交易对为: SAHARAUSDT, 24小时价格变化: 37.24%, 成交量: 2393183260.03 USDT
2025-06-27 20:14:05,316 - __main__ - INFO - 选择波动性最大的交易对: BTCUSDT
2025-06-27 20:14:05,316 - __main__ - INFO - 初始交易对设置为: BTCUSDT
2025-06-27 20:14:05,316 - __main__ - INFO - 正在创建WebSocket连接，准备订阅 BTCUSDT
2025-06-27 20:14:05,316 - __main__ - INFO - 数据库处理线程已启动
2025-06-27 20:14:05,317 - __main__ - INFO - 数据库线程初始化时尝试立即保存一次
2025-06-27 20:14:05,317 - __main__ - INFO - 开始调度快照生成任务，每500毫秒生成一次订单簿快照
2025-06-27 20:14:05,317 - __main__ - INFO - 快照生成线程已启动
2025-06-27 20:14:05,317 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 0
2025-06-27 20:14:05,318 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:14:05,318 - __main__ - INFO - 等待WebSocket连接建立... (尝试 1/5)
2025-06-27 20:14:05,318 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:14:05,319 - __main__ - WARNING - 没有深度快照数据需要保存 - 检查take_snapshot函数是否正常运行
2025-06-27 20:14:05,595 - websocket - INFO - Websocket connected
2025-06-27 20:14:05,596 - __main__ - INFO - 已连接到Binance WebSocket，准备订阅 BTCUSDT 数据
2025-06-27 20:14:05,596 - __main__ - INFO - 已发送 BTCUSDT 订阅请求: 深度更新和聚合交易
2025-06-27 20:14:05,597 - __main__ - INFO - 尝试初始化订单簿 (第 1/5 次)
2025-06-27 20:14:05,597 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:05,597 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:05,605 - __main__ - WARNING - 订单簿初始化失败，2秒后重试...
2025-06-27 20:14:05,849 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:14:05,849 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:14:05,893 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:04.201000, 当前时间戳: 2025-06-27 20:14:05.893273, 时间差: 0:00:01.692273
2025-06-27 20:14:06,350 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:14:06,438 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:14:06,925 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:14:06,925 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:06,926 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:14:06,931 - __main__ - INFO - 收到第一个 btcusdt 深度更新 (U=7895641349150, u=7895641354872)，开始初始化订单簿
2025-06-27 20:14:06,931 - __main__ - INFO - 尝试初始化订单簿 (第 1/5 次)
2025-06-27 20:14:06,932 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:04.306000, 当前时间戳: 2025-06-27 20:14:06.932107, 时间差: 0:00:02.626107
2025-06-27 20:14:06,932 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:07,093 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:07,097 - __main__ - WARNING - 订单簿初始化失败，2秒后重试...
2025-06-27 20:14:07,355 - __main__ - INFO - WebSocket连接已建立，主线程继续执行
2025-06-27 20:14:07,437 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=33，卖单数=15
2025-06-27 20:14:07,438 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: False, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:14:07,438 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:07.438 [时间戳:1751026447438], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 21.37, 卖盘总量: 41.26, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 20:14:07,602 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:07,606 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:04.404000, 当前时间戳: 2025-06-27 20:14:07.606827, 时间差: 0:00:03.202827
2025-06-27 20:14:07,690 - __main__ - INFO - 尝试初始化订单簿 (第 2/5 次)
2025-06-27 20:14:07,777 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:07,778 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:07,781 - __main__ - WARNING - 订单簿初始化失败，4秒后重试...
2025-06-27 20:14:07,951 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=43，卖单数=21
2025-06-27 20:14:08,126 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:08.126 [时间戳:1751026448126], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 34.83, 卖盘总量: 49.32, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 20:14:08,299 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:08,303 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:04.507000, 当前时间戳: 2025-06-27 20:14:08.303502, 时间差: 0:00:03.796502
2025-06-27 20:14:08,500 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=59，卖单数=30
2025-06-27 20:14:08,501 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:08.501 [时间戳:1751026448501], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 30.89, 卖盘总量: 51.15, 点差: 0.0001%, 当前深度快照数量: 3
2025-06-27 20:14:08,891 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:08,897 - __main__ - INFO - 大量深度更新: btcusdt 有 51 个买单和 41 个卖单更新, ID从 7895641364797 更新到 7895641384489
2025-06-27 20:14:08,898 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:04.610000, 当前时间戳: 2025-06-27 20:14:08.898258, 时间差: 0:00:04.288258
2025-06-27 20:14:09,418 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=89，卖单数=51
2025-06-27 20:14:09,418 - __main__ - INFO - 尝试初始化订单簿 (第 2/5 次)
2025-06-27 20:14:09,418 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:09.418 [时间戳:1751026449418], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 27.23, 卖盘总量: 57.51, 点差: 0.0001%, 当前深度快照数量: 4
2025-06-27 20:14:09,419 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:09,419 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:09,422 - __main__ - WARNING - 订单簿初始化失败，4秒后重试...
2025-06-27 20:14:09,949 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=89，卖单数=51
2025-06-27 20:14:09,949 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:09,950 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:09.950 [时间戳:1751026449950], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 27.23, 卖盘总量: 57.51, 点差: 0.0001%, 当前深度快照数量: 5
2025-06-27 20:14:09,956 - __main__ - INFO - 大量深度更新: btcusdt 有 85 个买单和 66 个卖单更新, ID从 7895641384489 更新到 7895641399073
2025-06-27 20:14:09,957 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:04.709000, 当前时间戳: 2025-06-27 20:14:09.957767, 时间差: 0:00:05.248767
2025-06-27 20:14:10,457 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=132，卖单数=79
2025-06-27 20:14:10,458 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:10.458 [时间戳:1751026450458], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.19, 卖盘总量: 40.26, 点差: 0.0001%, 当前深度快照数量: 6
2025-06-27 20:14:10,614 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:10,620 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:04.816000, 当前时间戳: 2025-06-27 20:14:10.620299, 时间差: 0:00:05.804299
2025-06-27 20:14:11,022 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=151，卖单数=98
2025-06-27 20:14:11,102 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:11.101 [时间戳:1751026451101], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 25.98, 卖盘总量: 36.68, 点差: 0.0001%, 当前深度快照数量: 7
2025-06-27 20:14:11,588 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=151，卖单数=98
2025-06-27 20:14:11,592 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:11,595 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:11.594 [时间戳:1751026451595], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 25.98, 卖盘总量: 36.68, 点差: 0.0001%, 当前深度快照数量: 8
2025-06-27 20:14:11,630 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.70, 数量: 0.002, 时间: 1751026444747
2025-06-27 20:14:11,810 - __main__ - INFO - 尝试初始化订单簿 (第 3/5 次)
2025-06-27 20:14:11,811 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:11,812 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:11,816 - __main__ - WARNING - 订单簿初始化失败，8秒后重试...
2025-06-27 20:14:12,376 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=160，卖单数=104
2025-06-27 20:14:12,377 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:12.377 [时间戳:1751026452377], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.06, 卖盘总量: 36.81, 点差: 0.0001%, 当前深度快照数量: 9
2025-06-27 20:14:12,520 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:04.918000, 当前时间戳: 2025-06-27 20:14:12.520113, 时间差: 0:00:07.602113
2025-06-27 20:14:12,940 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=160，卖单数=104
2025-06-27 20:14:13,025 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:13.025 [时间戳:1751026453025], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.06, 卖盘总量: 36.81, 点差: 0.0001%, 当前深度快照数量: 10
2025-06-27 20:14:13,188 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:13,193 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.021000, 当前时间戳: 2025-06-27 20:14:13.193172, 时间差: 0:00:08.172172
2025-06-27 20:14:13,437 - __main__ - INFO - 尝试初始化订单簿 (第 3/5 次)
2025-06-27 20:14:13,438 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:13,438 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:13,441 - __main__ - WARNING - 订单簿初始化失败，8秒后重试...
2025-06-27 20:14:13,465 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=175，卖单数=112
2025-06-27 20:14:13,466 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:13.466 [时间戳:1751026453466], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 25.70, 卖盘总量: 35.53, 点差: 0.0001%, 当前深度快照数量: 11
2025-06-27 20:14:13,992 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=175，卖单数=112
2025-06-27 20:14:13,994 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:13.994 [时间戳:1751026453994], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 25.70, 卖盘总量: 35.53, 点差: 0.0001%, 当前深度快照数量: 12
2025-06-27 20:14:14,159 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:14,164 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.121000, 当前时间戳: 2025-06-27 20:14:14.164142, 时间差: 0:00:09.043142
2025-06-27 20:14:12,693 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:12,698 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.60, 数量: 0.002, 时间: 1751026445025
2025-06-27 20:14:13,586 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.224000, 当前时间戳: 2025-06-27 20:14:13.586482, 时间差: 0:00:08.362482
2025-06-27 20:14:14,261 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:14,266 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.328000, 当前时间戳: 2025-06-27 20:14:14.266045, 时间差: 0:00:08.938045
2025-06-27 20:14:14,595 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=212，卖单数=140
2025-06-27 20:14:14,759 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:14.759 [时间戳:1751026454759], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 24.05, 卖盘总量: 37.78, 点差: 0.0001%, 当前深度快照数量: 13
2025-06-27 20:14:14,934 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:14,939 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.429000, 当前时间戳: 2025-06-27 20:14:14.939723, 时间差: 0:00:09.510723
2025-06-27 20:14:15,103 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=213，卖单数=146
2025-06-27 20:14:15,104 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:15.104 [时间戳:1751026455104], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 23.66, 卖盘总量: 38.02, 点差: 0.0001%, 当前深度快照数量: 14
2025-06-27 20:14:15,593 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:15,598 - __main__ - INFO - 大量深度更新: btcusdt 有 104 个买单和 44 个卖单更新, ID从 7895641431854 更新到 7895641446882
2025-06-27 20:14:15,599 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.532000, 当前时间戳: 2025-06-27 20:14:15.599021, 时间差: 0:00:10.067021
2025-06-27 20:14:15,671 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=243，卖单数=148
2025-06-27 20:14:15,747 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:15.747 [时间戳:1751026455747], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 23.07, 卖盘总量: 39.59, 点差: 0.0001%, 当前深度快照数量: 15
2025-06-27 20:14:16,215 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=243，卖单数=148
2025-06-27 20:14:16,647 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:16.647 [时间戳:1751026456647], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 23.07, 卖盘总量: 39.59, 点差: 0.0001%, 当前深度快照数量: 16
2025-06-27 20:14:16,789 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=243，卖单数=148
2025-06-27 20:14:16,789 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:16,789 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:16.789 [时间戳:1751026456789], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 23.07, 卖盘总量: 39.59, 点差: 0.0001%, 当前深度快照数量: 17
2025-06-27 20:14:16,794 - __main__ - INFO - 大量深度更新: btcusdt 有 84 个买单和 23 个卖单更新, ID从 7895641446882 更新到 7895641461696
2025-06-27 20:14:16,794 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.630000, 当前时间戳: 2025-06-27 20:14:16.794827, 时间差: 0:00:11.164827
2025-06-27 20:14:17,333 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=273，卖单数=151
2025-06-27 20:14:17,334 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:17.334 [时间戳:1751026457334], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 24.21, 卖盘总量: 36.02, 点差: 0.0001%, 当前深度快照数量: 18
2025-06-27 20:14:17,445 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:17,449 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.60, 数量: 0.394, 时间: 1751026445700
2025-06-27 20:14:17,789 - __main__ - INFO - 尝试初始化订单簿 (第 4/5 次)
2025-06-27 20:14:17,869 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:17,946 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:17,947 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:17.947 [时间戳:1751026457947], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 19
2025-06-27 20:14:17,947 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:17,951 - __main__ - WARNING - 订单簿初始化失败，16秒后重试...
2025-06-27 20:14:18,114 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.50, 数量: 0.002, 时间: 1751026445700
2025-06-27 20:14:18,449 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:18,615 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:18.615 [时间戳:1751026458615], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 20
2025-06-27 20:14:18,787 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.002, 时间: 1751026445700
2025-06-27 20:14:18,984 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:18,985 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:18.985 [时间戳:1751026458985], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 21
2025-06-27 20:14:19,372 - __main__ - INFO - 尝试初始化订单簿 (第 4/5 次)
2025-06-27 20:14:19,372 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106921.70, 数量: 0.002, 时间: 1751026445700
2025-06-27 20:14:19,373 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:19,541 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:19,541 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:19,541 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:19.541 [时间戳:1751026459541], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 22
2025-06-27 20:14:19,545 - __main__ - WARNING - 订单簿初始化失败，16秒后重试...
2025-06-27 20:14:20,062 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:20,062 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106921.60, 数量: 0.001, 时间: 1751026445700
2025-06-27 20:14:20,063 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:20.062 [时间戳:1751026460062], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 23
2025-06-27 20:14:20,605 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:20,606 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:20.606 [时间戳:1751026460606], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 24
2025-06-27 20:14:20,715 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106921.50, 数量: 0.004, 时间: 1751026445700
2025-06-27 20:14:21,138 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:21,217 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:21.217 [时间戳:1751026461217], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 25
2025-06-27 20:14:21,366 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106920.90, 数量: 0.038, 时间: 1751026445700
2025-06-27 20:14:21,750 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:21,824 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:21.823 [时间戳:1751026461823], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 26
2025-06-27 20:14:22,263 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:23,624 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:23.624 [时间戳:1751026463624], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 27
2025-06-27 20:14:23,677 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:23,677 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:23.677 [时间戳:1751026463677], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 28
2025-06-27 20:14:23,767 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106920.40, 数量: 0.002, 时间: 1751026445700
2025-06-27 20:14:24,183 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:24,593 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:24.593 [时间戳:1751026464593], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 29
2025-06-27 20:14:24,753 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:24,754 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106920.00, 数量: 0.003, 时间: 1751026445700
2025-06-27 20:14:24,754 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:24.754 [时间戳:1751026464754], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 30
2025-06-27 20:14:25,290 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:25,291 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:25.290 [时间戳:1751026465291], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 31
2025-06-27 20:14:25,397 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106919.50, 数量: 0.005, 时间: 1751026445700
2025-06-27 20:14:25,627 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 31
2025-06-27 20:14:25,628 - __main__ - INFO - 准备保存 31 条快照数据，并已清空全局快照列表
2025-06-27 20:14:25,850 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:26,012 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:26.012 [时间戳:1751026466012], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 20:14:26,176 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106919.40, 数量: 0.002, 时间: 1751026445700
2025-06-27 20:14:26,581 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:26,670 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:26.670 [时间戳:1751026466670], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 20:14:27,124 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:27,205 - __main__ - INFO - 成功保存 31 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:14:27,611 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:27.611 [时间戳:1751026467611], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 3
2025-06-27 20:14:27,925 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:27,998 - __main__ - INFO - 共保存了 31 条深度快照数据到MariaDB
2025-06-27 20:14:27,998 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:27.998 [时间戳:1751026467998], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 4
2025-06-27 20:14:27,999 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.735000, 当前时间戳: 2025-06-27 20:14:27.999756, 时间差: 0:00:22.264756
2025-06-27 20:14:28,813 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=282，卖单数=155
2025-06-27 20:14:28,814 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:28.814 [时间戳:1751026468814], 买盘最高价: 106922.6, 卖盘最低价: 106922.7, 买盘总量: 26.10, 卖盘总量: 35.63, 点差: 0.0001%, 当前深度快照数量: 5
2025-06-27 20:14:28,981 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:28,986 - __main__ - INFO - 大量深度更新: btcusdt 有 227 个买单和 191 个卖单更新, ID从 7895641469312 更新到 7895641480243
2025-06-27 20:14:28,986 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106919.00, 数量: 0.223, 时间: 1751026445700
2025-06-27 20:14:29,637 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=373，卖单数=249
2025-06-27 20:14:29,805 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:29.805 [时间戳:1751026469805], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 34.51, 卖盘总量: 31.59, 点差: 0.0001%, 当前深度快照数量: 6
2025-06-27 20:14:29,975 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.837000, 当前时间戳: 2025-06-27 20:14:29.975540, 时间差: 0:00:24.138540
2025-06-27 20:14:30,462 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=373，卖单数=249
2025-06-27 20:14:30,464 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:30.463 [时间戳:1751026470463], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 34.51, 卖盘总量: 31.59, 点差: 0.0001%, 当前深度快照数量: 7
2025-06-27 20:14:30,970 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=373，卖单数=249
2025-06-27 20:14:31,135 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:31.135 [时间戳:1751026471135], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 34.51, 卖盘总量: 31.59, 点差: 0.0001%, 当前深度快照数量: 8
2025-06-27 20:14:31,303 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:31,308 - __main__ - INFO - 大量深度更新: btcusdt 有 151 个买单和 115 个卖单更新, ID从 7895641480243 更新到 7895641509618
2025-06-27 20:14:31,308 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.032, 时间: 1751026445763
2025-06-27 20:14:31,506 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=408，卖单数=295
2025-06-27 20:14:31,507 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:31.507 [时间戳:1751026471507], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 18.89, 卖盘总量: 30.17, 点差: 0.0001%, 当前深度快照数量: 9
2025-06-27 20:14:31,886 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:05.937000, 当前时间戳: 2025-06-27 20:14:31.886570, 时间差: 0:00:25.949570
2025-06-27 20:14:32,050 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=408，卖单数=295
2025-06-27 20:14:32,051 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:32.051 [时间戳:1751026472051], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 18.89, 卖盘总量: 30.17, 点差: 0.0001%, 当前深度快照数量: 10
2025-06-27 20:14:32,625 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=408，卖单数=295
2025-06-27 20:14:32,710 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:32.710 [时间戳:1751026472710], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 18.89, 卖盘总量: 30.17, 点差: 0.0001%, 当前深度快照数量: 11
2025-06-27 20:14:32,879 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:32,884 - __main__ - INFO - 大量深度更新: btcusdt 有 83 个买单和 76 个卖单更新, ID从 7895641509618 更新到 7895641530722
2025-06-27 20:14:32,885 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.039000, 当前时间戳: 2025-06-27 20:14:32.885136, 时间差: 0:00:26.846136
2025-06-27 20:14:33,381 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=441，卖单数=313
2025-06-27 20:14:33,382 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:33.382 [时间戳:1751026473382], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 20.50, 卖盘总量: 31.03, 点差: 0.0001%, 当前深度快照数量: 12
2025-06-27 20:14:33,874 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:33,879 - __main__ - INFO - 大量深度更新: btcusdt 有 74 个买单和 57 个卖单更新, ID从 7895641530722 更新到 7895641538506
2025-06-27 20:14:33,880 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.141000, 当前时间戳: 2025-06-27 20:14:33.880187, 时间差: 0:00:27.739187
2025-06-27 20:14:33,969 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=461，卖单数=323
2025-06-27 20:14:33,969 - __main__ - INFO - 尝试初始化订单簿 (第 5/5 次)
2025-06-27 20:14:34,057 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:34.056 [时间戳:1751026474056], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 23.63, 卖盘总量: 31.32, 点差: 0.0001%, 当前深度快照数量: 13
2025-06-27 20:14:34,057 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:34,057 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:34,060 - __main__ - ERROR - 订单簿初始化失败，已达到最大重试次数 5
2025-06-27 20:14:34,061 - __main__ - ERROR - 所有初始化尝试均失败，可能需要检查API连接或限制
2025-06-27 20:14:34,563 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=461，卖单数=323
2025-06-27 20:14:34,751 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:34.751 [时间戳:1751026474751], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 23.63, 卖盘总量: 31.32, 点差: 0.0001%, 当前深度快照数量: 14
2025-06-27 20:14:34,934 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:34,939 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.241000, 当前时间戳: 2025-06-27 20:14:34.939044, 时间差: 0:00:28.698044
2025-06-27 20:14:35,407 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=466，卖单数=328
2025-06-27 20:14:35,408 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:35.408 [时间戳:1751026475408], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.30, 卖盘总量: 31.33, 点差: 0.0001%, 当前深度快照数量: 15
2025-06-27 20:14:35,409 - __main__ - INFO - 快照调度状态：过去30秒新增了 49 个快照，当前总计 15 个快照
2025-06-27 20:14:35,626 - __main__ - INFO - 尝试初始化订单簿 (第 5/5 次)
2025-06-27 20:14:35,698 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:14:35,699 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:14:35,703 - __main__ - ERROR - 订单簿初始化失败，已达到最大重试次数 5
2025-06-27 20:14:35,703 - __main__ - ERROR - 所有初始化尝试均失败，可能需要检查API连接或限制
2025-06-27 20:14:36,165 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=466，卖单数=328
2025-06-27 20:14:36,165 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:36,166 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:36.166 [时间戳:1751026476166], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.30, 卖盘总量: 31.33, 点差: 0.0001%, 当前深度快照数量: 16
2025-06-27 20:14:36,170 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.345000, 当前时间戳: 2025-06-27 20:14:36.170074, 时间差: 0:00:29.825074
2025-06-27 20:14:36,934 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=478，卖单数=333
2025-06-27 20:14:37,022 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:37.022 [时间戳:1751026477022], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.44, 卖盘总量: 31.37, 点差: 0.0001%, 当前深度快照数量: 17
2025-06-27 20:14:37,188 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:37,194 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.447000, 当前时间戳: 2025-06-27 20:14:37.194014, 时间差: 0:00:30.747014
2025-06-27 20:14:37,577 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=486，卖单数=335
2025-06-27 20:14:37,662 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:37.662 [时间戳:1751026477662], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.13, 卖盘总量: 31.37, 点差: 0.0001%, 当前深度快照数量: 18
2025-06-27 20:14:38,160 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=486，卖单数=335
2025-06-27 20:14:38,160 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:38,161 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:38.161 [时间戳:1751026478161], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.13, 卖盘总量: 31.37, 点差: 0.0001%, 当前深度快照数量: 19
2025-06-27 20:14:38,165 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.550000, 当前时间戳: 2025-06-27 20:14:38.165454, 时间差: 0:00:31.615454
2025-06-27 20:14:38,664 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=492，卖单数=343
2025-06-27 20:14:38,665 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:38.665 [时间戳:1751026478665], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.74, 卖盘总量: 31.37, 点差: 0.0001%, 当前深度快照数量: 20
2025-06-27 20:14:38,839 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:38,843 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.643000, 当前时间戳: 2025-06-27 20:14:38.843629, 时间差: 0:00:32.200629
2025-06-27 20:14:39,210 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=493，卖单数=345
2025-06-27 20:14:39,282 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:39.282 [时间戳:1751026479282], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.75, 卖盘总量: 31.38, 点差: 0.0001%, 当前深度快照数量: 21
2025-06-27 20:14:39,426 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:39,431 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.756000, 当前时间戳: 2025-06-27 20:14:39.431048, 时间差: 0:00:32.675048
2025-06-27 20:14:39,754 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=492，卖单数=347
2025-06-27 20:14:39,908 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:39.908 [时间戳:1751026479908], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.75, 卖盘总量: 31.38, 点差: 0.0001%, 当前深度快照数量: 22
2025-06-27 20:14:40,076 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:40,081 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.858000, 当前时间戳: 2025-06-27 20:14:40.081572, 时间差: 0:00:33.223572
2025-06-27 20:14:40,287 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=504，卖单数=350
2025-06-27 20:14:40,288 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:40.288 [时间戳:1751026480288], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.75, 卖盘总量: 31.18, 点差: 0.0001%, 当前深度快照数量: 23
2025-06-27 20:14:40,723 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:40,728 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:06.961000, 当前时间戳: 2025-06-27 20:14:40.728438, 时间差: 0:00:33.767438
2025-06-27 20:14:41,152 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=511，卖单数=360
2025-06-27 20:14:41,235 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:41.235 [时间戳:1751026481235], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.53, 卖盘总量: 31.93, 点差: 0.0001%, 当前深度快照数量: 24
2025-06-27 20:14:41,737 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=511，卖单数=360
2025-06-27 20:14:41,913 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:41.913 [时间戳:1751026481913], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.53, 卖盘总量: 31.93, 点差: 0.0001%, 当前深度快照数量: 25
2025-06-27 20:14:42,079 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:42,090 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.061000, 当前时间戳: 2025-06-27 20:14:42.090179, 时间差: 0:00:35.029179
2025-06-27 20:14:42,287 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=517，卖单数=365
2025-06-27 20:14:42,289 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:42.289 [时间戳:1751026482289], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.00, 卖盘总量: 31.95, 点差: 0.0001%, 当前深度快照数量: 26
2025-06-27 20:14:42,826 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=517，卖单数=365
2025-06-27 20:14:42,827 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:42.827 [时间戳:1751026482827], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.00, 卖盘总量: 31.95, 点差: 0.0001%, 当前深度快照数量: 27
2025-06-27 20:14:42,970 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:42,974 - __main__ - INFO - 大量深度更新: btcusdt 有 59 个买单和 31 个卖单更新, ID从 7895641584078 更新到 7895641588489
2025-06-27 20:14:42,975 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.166000, 当前时间戳: 2025-06-27 20:14:42.975236, 时间差: 0:00:35.809236
2025-06-27 20:14:43,366 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=530，卖单数=367
2025-06-27 20:14:43,444 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:43.444 [时间戳:1751026483444], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.79, 卖盘总量: 31.95, 点差: 0.0001%, 当前深度快照数量: 28
2025-06-27 20:14:43,600 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:43,604 - __main__ - INFO - 大量深度更新: btcusdt 有 51 个买单和 42 个卖单更新, ID从 7895641588489 更新到 7895641592514
2025-06-27 20:14:43,605 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.263000, 当前时间戳: 2025-06-27 20:14:43.605260, 时间差: 0:00:36.342260
2025-06-27 20:14:43,944 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=538，卖单数=374
2025-06-27 20:14:44,111 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:44.111 [时间戳:1751026484111], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.81, 卖盘总量: 31.97, 点差: 0.0001%, 当前深度快照数量: 29
2025-06-27 20:14:44,260 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:44,265 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.001, 时间: 1751026447193
2025-06-27 20:14:44,485 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=540，卖单数=378
2025-06-27 20:14:44,486 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:44.485 [时间戳:1751026484485], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.67, 卖盘总量: 31.97, 点差: 0.0001%, 当前深度快照数量: 30
2025-06-27 20:14:43,146 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.371000, 当前时间戳: 2025-06-27 20:14:43.146062, 时间差: 0:00:35.775062
2025-06-27 20:14:44,035 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:44,039 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.471000, 当前时间戳: 2025-06-27 20:14:44.039404, 时间差: 0:00:36.568404
2025-06-27 20:14:45,002 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=547，卖单数=382
2025-06-27 20:14:45,003 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:45.003 [时间戳:1751026485003], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.81, 卖盘总量: 31.97, 点差: 0.0001%, 当前深度快照数量: 31
2025-06-27 20:14:45,302 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:45,306 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.578000, 当前时间戳: 2025-06-27 20:14:45.306446, 时间差: 0:00:37.728446
2025-06-27 20:14:45,528 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=552，卖单数=387
2025-06-27 20:14:45,529 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:45.529 [时间戳:1751026485529], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.48, 卖盘总量: 31.97, 点差: 0.0001%, 当前深度快照数量: 32
2025-06-27 20:14:45,968 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:45,973 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.678000, 当前时间戳: 2025-06-27 20:14:45.973450, 时间差: 0:00:38.295450
2025-06-27 20:14:46,053 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=551，卖单数=391
2025-06-27 20:14:46,455 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:46.455 [时间戳:1751026486455], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 27.12, 卖盘总量: 31.96, 点差: 0.0001%, 当前深度快照数量: 33
2025-06-27 20:14:46,621 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=551，卖单数=391
2025-06-27 20:14:47,107 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:47.107 [时间戳:1751026487107], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 27.12, 卖盘总量: 31.96, 点差: 0.0001%, 当前深度快照数量: 34
2025-06-27 20:14:47,159 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=551，卖单数=391
2025-06-27 20:14:47,159 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:47.159 [时间戳:1751026487159], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 27.12, 卖盘总量: 31.96, 点差: 0.0001%, 当前深度快照数量: 35
2025-06-27 20:14:47,270 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:47,275 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.781000, 当前时间戳: 2025-06-27 20:14:47.275429, 时间差: 0:00:39.494429
2025-06-27 20:14:47,709 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=567，卖单数=394
2025-06-27 20:14:47,710 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:47.710 [时间戳:1751026487710], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.09, 卖盘总量: 31.96, 点差: 0.0001%, 当前深度快照数量: 36
2025-06-27 20:14:47,851 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:47,856 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.883000, 当前时间戳: 2025-06-27 20:14:47.856886, 时间差: 0:00:39.973886
2025-06-27 20:14:48,497 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 36，订单簿状态: 初始化=False，买单数=567，卖单数=400
2025-06-27 20:14:48,497 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 36
2025-06-27 20:14:48,651 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:48.651 [时间戳:1751026488651], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 27.68, 卖盘总量: 30.61, 点差: 0.0001%, 当前深度快照数量: 37
2025-06-27 20:14:48,651 - __main__ - INFO - 准备保存 37 条快照数据，并已清空全局快照列表
2025-06-27 20:14:48,902 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:48,906 - __main__ - INFO - 大量深度更新: btcusdt 有 146 个买单和 76 个卖单更新, ID从 7895641619336 更新到 7895641626993
2025-06-27 20:14:48,906 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.001, 时间: 1751026447735
2025-06-27 20:14:49,072 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=612，卖单数=425
2025-06-27 20:14:49,073 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:49.073 [时间戳:1751026489073], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 18.88, 卖盘总量: 31.00, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 20:14:49,653 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=612，卖单数=425
2025-06-27 20:14:49,653 - __main__ - INFO - 成功保存 37 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:14:49,820 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:49.820 [时间戳:1751026489820], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 18.88, 卖盘总量: 31.00, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 20:14:50,075 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:07.983000, 当前时间戳: 2025-06-27 20:14:50.075869, 时间差: 0:00:42.092869
2025-06-27 20:14:50,220 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=612，卖单数=425
2025-06-27 20:14:50,299 - __main__ - INFO - 共保存了 37 条深度快照数据到MariaDB
2025-06-27 20:14:50,300 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:50.299 [时间戳:1751026490299], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 18.88, 卖盘总量: 31.00, 点差: 0.0001%, 当前深度快照数量: 3
2025-06-27 20:14:50,735 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=612，卖单数=425
2025-06-27 20:14:50,735 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:50,736 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:50.736 [时间戳:1751026490736], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 18.88, 卖盘总量: 31.00, 点差: 0.0001%, 当前深度快照数量: 4
2025-06-27 20:14:50,740 - __main__ - INFO - 大量深度更新: btcusdt 有 107 个买单和 74 个卖单更新, ID从 7895641626993 更新到 7895641638068
2025-06-27 20:14:50,741 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.085000, 当前时间戳: 2025-06-27 20:14:50.741530, 时间差: 0:00:42.656530
2025-06-27 20:14:51,270 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=634，卖单数=434
2025-06-27 20:14:51,270 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:51.270 [时间戳:1751026491270], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.46, 卖盘总量: 29.11, 点差: 0.0001%, 当前深度快照数量: 5
2025-06-27 20:14:51,703 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:51,707 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.188000, 当前时间戳: 2025-06-27 20:14:51.707859, 时间差: 0:00:43.519859
2025-06-27 20:14:51,792 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=643，卖单数=433
2025-06-27 20:14:51,876 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:51.876 [时间戳:1751026491876], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.44, 卖盘总量: 29.93, 点差: 0.0001%, 当前深度快照数量: 6
2025-06-27 20:14:52,543 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=643，卖单数=433
2025-06-27 20:14:52,544 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:52.544 [时间戳:1751026492544], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.44, 卖盘总量: 29.93, 点差: 0.0001%, 当前深度快照数量: 7
2025-06-27 20:14:52,722 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:52,727 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.293000, 当前时间戳: 2025-06-27 20:14:52.727451, 时间差: 0:00:44.434451
2025-06-27 20:14:53,229 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=645，卖单数=436
2025-06-27 20:14:53,230 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:53.230 [时间戳:1751026493230], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.03, 卖盘总量: 30.83, 点差: 0.0001%, 当前深度快照数量: 8
2025-06-27 20:14:54,142 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=645，卖单数=436
2025-06-27 20:14:54,224 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:54.224 [时间戳:1751026494224], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.03, 卖盘总量: 30.83, 点差: 0.0001%, 当前深度快照数量: 9
2025-06-27 20:14:54,387 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:54,391 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.100, 时间: 1751026448181
2025-06-27 20:14:54,677 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=647，卖单数=436
2025-06-27 20:14:54,819 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:54.819 [时间戳:1751026494819], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.25, 卖盘总量: 29.89, 点差: 0.0001%, 当前深度快照数量: 10
2025-06-27 20:14:54,962 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.394000, 当前时间戳: 2025-06-27 20:14:54.962705, 时间差: 0:00:46.568705
2025-06-27 20:14:55,178 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=647，卖单数=436
2025-06-27 20:14:55,178 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:55.178 [时间戳:1751026495178], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.25, 卖盘总量: 29.89, 点差: 0.0001%, 当前深度快照数量: 11
2025-06-27 20:14:55,705 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=647，卖单数=436
2025-06-27 20:14:55,706 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:55.706 [时间戳:1751026495706], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.25, 卖盘总量: 29.89, 点差: 0.0001%, 当前深度快照数量: 12
2025-06-27 20:14:56,252 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=647，卖单数=436
2025-06-27 20:14:56,253 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:56.253 [时间戳:1751026496253], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.25, 卖盘总量: 29.89, 点差: 0.0001%, 当前深度快照数量: 13
2025-06-27 20:14:57,038 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=647，卖单数=436
2025-06-27 20:14:57,054 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:57.054 [时间戳:1751026497054], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.25, 卖盘总量: 29.89, 点差: 0.0001%, 当前深度快照数量: 14
2025-06-27 20:14:57,190 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:57,194 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.498000, 当前时间戳: 2025-06-27 20:14:57.194927, 时间差: 0:00:48.696927
2025-06-27 20:14:57,709 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=647，卖单数=440
2025-06-27 20:14:57,710 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:57.710 [时间戳:1751026497710], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.36, 卖盘总量: 30.84, 点差: 0.0001%, 当前深度快照数量: 15
2025-06-27 20:14:58,228 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=647，卖单数=440
2025-06-27 20:14:58,228 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:14:58,229 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:58.229 [时间戳:1751026498229], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.36, 卖盘总量: 30.84, 点差: 0.0001%, 当前深度快照数量: 16
2025-06-27 20:14:58,233 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.009, 时间: 1751026448432
2025-06-27 20:14:58,737 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=650，卖单数=442
2025-06-27 20:14:58,738 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:58.738 [时间戳:1751026498738], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.33, 卖盘总量: 30.83, 点差: 0.0001%, 当前深度快照数量: 17
2025-06-27 20:14:59,440 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=650，卖单数=442
2025-06-27 20:14:59,441 - __main__ - INFO - 深度快照已生成: solusdt @ 20:14:59.441 [时间戳:1751026499441], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.33, 卖盘总量: 30.83, 点差: 0.0001%, 当前深度快照数量: 18
2025-06-27 20:14:59,606 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.600000, 当前时间戳: 2025-06-27 20:14:59.606539, 时间差: 0:00:51.006539
2025-06-27 20:14:59,978 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=650，卖单数=442
2025-06-27 20:15:00,052 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:00.052 [时间戳:1751026500052], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.33, 卖盘总量: 30.83, 点差: 0.0001%, 当前深度快照数量: 19
2025-06-27 20:15:00,200 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:00,205 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.701000, 当前时间戳: 2025-06-27 20:15:00.205174, 时间差: 0:00:51.504174
2025-06-27 20:15:00,544 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=658，卖单数=446
2025-06-27 20:15:00,710 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:00.710 [时间戳:1751026500710], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.43, 卖盘总量: 30.76, 点差: 0.0001%, 当前深度快照数量: 20
2025-06-27 20:15:00,872 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:00,877 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.801000, 当前时间戳: 2025-06-27 20:15:00.877628, 时间差: 0:00:52.076628
2025-06-27 20:15:01,091 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=662，卖单数=447
2025-06-27 20:15:01,091 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:01.091 [时间戳:1751026501091], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.44, 卖盘总量: 30.86, 点差: 0.0001%, 当前深度快照数量: 21
2025-06-27 20:15:01,558 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:01,567 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:08.902000, 当前时间戳: 2025-06-27 20:15:01.567308, 时间差: 0:00:52.665308
2025-06-27 20:15:01,660 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=664，卖单数=451
2025-06-27 20:15:01,745 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:01.745 [时间戳:1751026501745], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.44, 卖盘总量: 30.81, 点差: 0.0001%, 当前深度快照数量: 22
2025-06-27 20:15:02,247 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=664，卖单数=451
2025-06-27 20:15:02,247 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:02,248 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:02.248 [时间戳:1751026502248], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.44, 卖盘总量: 30.81, 点差: 0.0001%, 当前深度快照数量: 23
2025-06-27 20:15:02,252 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.005000, 当前时间戳: 2025-06-27 20:15:02.252162, 时间差: 0:00:53.247162
2025-06-27 20:15:02,784 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=667，卖单数=456
2025-06-27 20:15:02,784 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:02.784 [时间戳:1751026502784], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.31, 卖盘总量: 30.73, 点差: 0.0001%, 当前深度快照数量: 24
2025-06-27 20:15:03,163 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:03,167 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.002, 时间: 1751026448916
2025-06-27 20:15:03,245 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: False, 缓存的深度更新: 0, 快照数量: 24
2025-06-27 20:15:03,326 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=674，卖单数=460
2025-06-27 20:15:03,326 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:03.326 [时间戳:1751026503326], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.99, 卖盘总量: 30.92, 点差: 0.0001%, 当前深度快照数量: 25
2025-06-27 20:15:03,894 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=674，卖单数=460
2025-06-27 20:15:03,973 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:03.973 [时间戳:1751026503973], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.99, 卖盘总量: 30.92, 点差: 0.0001%, 当前深度快照数量: 26
2025-06-27 20:15:04,130 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.109000, 当前时间戳: 2025-06-27 20:15:04.130844, 时间差: 0:00:55.021844
2025-06-27 20:15:04,465 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=674，卖单数=460
2025-06-27 20:15:04,630 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:04.629 [时间戳:1751026504629], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.99, 卖盘总量: 30.92, 点差: 0.0001%, 当前深度快照数量: 27
2025-06-27 20:15:04,809 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:04,814 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.211000, 当前时间戳: 2025-06-27 20:15:04.814508, 时间差: 0:00:55.603508
2025-06-27 20:15:05,324 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=677，卖单数=462
2025-06-27 20:15:05,324 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:05.324 [时间戳:1751026505324], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.56, 卖盘总量: 30.93, 点差: 0.0001%, 当前深度快照数量: 28
2025-06-27 20:15:05,433 - __main__ - INFO - 快照调度状态：过去30秒新增了 50 个快照，当前总计 28 个快照
2025-06-27 20:15:05,670 - __main__ - INFO - 数据库处理状态：已执行 0 次保存操作
2025-06-27 20:15:05,839 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=677，卖单数=462
2025-06-27 20:15:06,009 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:06.008 [时间戳:1751026506008], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.56, 卖盘总量: 30.93, 点差: 0.0001%, 当前深度快照数量: 29
2025-06-27 20:15:06,179 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:06,184 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.001, 时间: 1751026449089
2025-06-27 20:15:06,385 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=680，卖单数=469
2025-06-27 20:15:06,386 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:06.385 [时间戳:1751026506386], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.56, 卖盘总量: 30.99, 点差: 0.0001%, 当前深度快照数量: 30
2025-06-27 20:15:06,777 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.312000, 当前时间戳: 2025-06-27 20:15:06.777176, 时间差: 0:00:57.465176
2025-06-27 20:15:06,940 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=680，卖单数=469
2025-06-27 20:15:06,941 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:06.941 [时间戳:1751026506941], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.56, 卖盘总量: 30.99, 点差: 0.0001%, 当前深度快照数量: 31
2025-06-27 20:15:07,441 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=680，卖单数=469
2025-06-27 20:15:07,441 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:07,442 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:07.442 [时间戳:1751026507442], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.56, 卖盘总量: 30.99, 点差: 0.0001%, 当前深度快照数量: 32
2025-06-27 20:15:07,446 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.416000, 当前时间戳: 2025-06-27 20:15:07.446741, 时间差: 0:00:58.030741
2025-06-27 20:15:07,966 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=680，卖单数=470
2025-06-27 20:15:07,967 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:07.966 [时间戳:1751026507966], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.09, 卖盘总量: 30.86, 点差: 0.0001%, 当前深度快照数量: 33
2025-06-27 20:15:08,145 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:08,150 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.518000, 当前时间戳: 2025-06-27 20:15:08.150079, 时间差: 0:00:58.632079
2025-06-27 20:15:08,511 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=681，卖单数=471
2025-06-27 20:15:08,672 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:08.672 [时间戳:1751026508672], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.81, 卖盘总量: 30.88, 点差: 0.0001%, 当前深度快照数量: 34
2025-06-27 20:15:08,846 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:08,851 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.002, 时间: 1751026449416
2025-06-27 20:15:09,327 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=692，卖单数=480
2025-06-27 20:15:09,328 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:09.328 [时间戳:1751026509328], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.91, 卖盘总量: 30.88, 点差: 0.0001%, 当前深度快照数量: 35
2025-06-27 20:15:09,771 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.621000, 当前时间戳: 2025-06-27 20:15:09.771580, 时间差: 0:01:00.150580
2025-06-27 20:15:09,850 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=692，卖单数=480
2025-06-27 20:15:09,931 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:09.931 [时间戳:1751026509931], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.91, 卖盘总量: 30.88, 点差: 0.0001%, 当前深度快照数量: 36
2025-06-27 20:15:10,427 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 36，订单簿状态: 初始化=False，买单数=692，卖单数=480
2025-06-27 20:15:10,427 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:10,427 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:10.427 [时间戳:1751026510427], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.91, 卖盘总量: 30.88, 点差: 0.0001%, 当前深度快照数量: 37
2025-06-27 20:15:10,432 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.720000, 当前时间戳: 2025-06-27 20:15:10.432033, 时间差: 0:01:00.712033
2025-06-27 20:15:10,772 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 37
2025-06-27 20:15:10,937 - __main__ - INFO - 准备保存 37 条快照数据，并已清空全局快照列表
2025-06-27 20:15:11,425 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=699，卖单数=489
2025-06-27 20:15:11,506 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:11,510 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:11.510 [时间戳:1751026511510], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.91, 卖盘总量: 30.87, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 20:15:11,514 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.827000, 当前时间戳: 2025-06-27 20:15:11.514521, 时间差: 0:01:01.687521
2025-06-27 20:15:11,587 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=703，卖单数=490
2025-06-27 20:15:11,661 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:11.661 [时间戳:1751026511661], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.54, 卖盘总量: 30.87, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 20:15:12,146 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=703，卖单数=490
2025-06-27 20:15:12,147 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:12,148 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:12.148 [时间戳:1751026512148], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.54, 卖盘总量: 30.87, 点差: 0.0001%, 当前深度快照数量: 3
2025-06-27 20:15:12,153 - __main__ - INFO - 大量深度更新: btcusdt 有 51 个买单和 35 个卖单更新, ID从 7895641715301 更新到 7895641720756
2025-06-27 20:15:12,157 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.001, 时间: 1751026449772
2025-06-27 20:15:12,448 - __main__ - INFO - 成功保存 37 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:15:12,683 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=711，卖单数=493
2025-06-27 20:15:12,843 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:12.843 [时间戳:1751026512843], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.83, 卖盘总量: 29.35, 点差: 0.0001%, 当前深度快照数量: 4
2025-06-27 20:15:13,084 - __main__ - INFO - 共保存了 37 条深度快照数据到MariaDB
2025-06-27 20:15:13,084 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:09.927000, 当前时间戳: 2025-06-27 20:15:13.084682, 时间差: 0:01:03.157682
2025-06-27 20:15:13,254 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=711，卖单数=493
2025-06-27 20:15:13,255 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:13.255 [时间戳:1751026513255], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.83, 卖盘总量: 29.35, 点差: 0.0001%, 当前深度快照数量: 5
2025-06-27 20:15:13,819 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=711，卖单数=493
2025-06-27 20:15:13,898 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:13.898 [时间戳:1751026513898], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.83, 卖盘总量: 29.35, 点差: 0.0001%, 当前深度快照数量: 6
2025-06-27 20:15:14,061 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:14,065 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.033000, 当前时间戳: 2025-06-27 20:15:14.065858, 时间差: 0:01:04.032858
2025-06-27 20:15:14,690 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=711，卖单数=496
2025-06-27 20:15:14,831 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:14.831 [时间戳:1751026514831], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.08, 卖盘总量: 29.72, 点差: 0.0001%, 当前深度快照数量: 7
2025-06-27 20:15:12,653 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:12,658 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.136000, 当前时间戳: 2025-06-27 20:15:12.658441, 时间差: 0:01:02.522441
2025-06-27 20:15:14,609 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:14,614 - __main__ - INFO - 大量深度更新: btcusdt 有 55 个买单和 33 个卖单更新, ID从 7895641730588 更新到 7895641738544
2025-06-27 20:15:14,614 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.002, 时间: 1751026450062
2025-06-27 20:15:15,736 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=720，卖单数=501
2025-06-27 20:15:15,737 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:15.737 [时间戳:1751026515737], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.22, 卖盘总量: 29.68, 点差: 0.0001%, 当前深度快照数量: 8
2025-06-27 20:15:15,905 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.238000, 当前时间戳: 2025-06-27 20:15:15.905549, 时间差: 0:01:05.667549
2025-06-27 20:15:16,303 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=720，卖单数=501
2025-06-27 20:15:16,384 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:16.384 [时间戳:1751026516384], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.22, 卖盘总量: 29.68, 点差: 0.0001%, 当前深度快照数量: 9
2025-06-27 20:15:16,550 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:16,555 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.338000, 当前时间戳: 2025-06-27 20:15:16.555094, 时间差: 0:01:06.217094
2025-06-27 20:15:16,845 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=725，卖单数=504
2025-06-27 20:15:16,987 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:16.987 [时间戳:1751026516987], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.34, 卖盘总量: 29.75, 点差: 0.0001%, 当前深度快照数量: 10
2025-06-27 20:15:17,451 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=725，卖单数=504
2025-06-27 20:15:17,451 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:17,451 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:17.451 [时间戳:1751026517451], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.34, 卖盘总量: 29.75, 点差: 0.0001%, 当前深度快照数量: 11
2025-06-27 20:15:17,455 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.442000, 当前时间戳: 2025-06-27 20:15:17.455637, 时间差: 0:01:07.013637
2025-06-27 20:15:18,104 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=732，卖单数=512
2025-06-27 20:15:18,249 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:18.249 [时间戳:1751026518249], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.67, 卖盘总量: 28.32, 点差: 0.0001%, 当前深度快照数量: 12
2025-06-27 20:15:18,413 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:18,418 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.541000, 当前时间戳: 2025-06-27 20:15:18.418962, 时间差: 0:01:07.877962
2025-06-27 20:15:18,639 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=732，卖单数=512
2025-06-27 20:15:18,640 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:18.640 [时间戳:1751026518640], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.03, 卖盘总量: 28.32, 点差: 0.0001%, 当前深度快照数量: 13
2025-06-27 20:15:19,076 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:19,082 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.645000, 当前时间戳: 2025-06-27 20:15:19.082473, 时间差: 0:01:08.437473
2025-06-27 20:15:19,165 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=735，卖单数=517
2025-06-27 20:15:19,561 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:19.561 [时间戳:1751026519561], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.06, 卖盘总量: 27.46, 点差: 0.0001%, 当前深度快照数量: 14
2025-06-27 20:15:19,727 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=735，卖单数=517
2025-06-27 20:15:19,896 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:19.896 [时间戳:1751026519896], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.06, 卖盘总量: 27.46, 点差: 0.0001%, 当前深度快照数量: 15
2025-06-27 20:15:20,055 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:20,060 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.008, 时间: 1751026450515
2025-06-27 20:15:20,500 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=741，卖单数=521
2025-06-27 20:15:20,500 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:20.500 [时间戳:1751026520500], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.84, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 16
2025-06-27 20:15:21,112 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=741，卖单数=521
2025-06-27 20:15:21,113 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:21.113 [时间戳:1751026521113], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.84, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 17
2025-06-27 20:15:21,254 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.747000, 当前时间戳: 2025-06-27 20:15:21.254229, 时间差: 0:01:10.507229
2025-06-27 20:15:22,004 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=741，卖单数=521
2025-06-27 20:15:22,083 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:22.083 [时间戳:1751026522083], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.84, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 18
2025-06-27 20:15:22,252 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:22,257 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.853000, 当前时间戳: 2025-06-27 20:15:22.257614, 时间差: 0:01:11.404614
2025-06-27 20:15:22,576 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=745，卖单数=520
2025-06-27 20:15:22,742 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:22.742 [时间戳:1751026522742], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.06, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 19
2025-06-27 20:15:22,902 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:22,908 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:10.954000, 当前时间戳: 2025-06-27 20:15:22.908380, 时间差: 0:01:11.954380
2025-06-27 20:15:23,303 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=753，卖单数=527
2025-06-27 20:15:23,386 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:23.386 [时间戳:1751026523386], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.94, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 20
2025-06-27 20:15:23,870 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=753，卖单数=527
2025-06-27 20:15:23,870 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:23,870 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:23.870 [时间戳:1751026523870], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.94, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 21
2025-06-27 20:15:23,874 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.001, 时间: 1751026450808
2025-06-27 20:15:24,477 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=754，卖单数=527
2025-06-27 20:15:24,924 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:24.924 [时间戳:1751026524924], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.94, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 22
2025-06-27 20:15:25,363 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=754，卖单数=527
2025-06-27 20:15:25,364 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.057000, 当前时间戳: 2025-06-27 20:15:25.364298, 时间差: 0:01:14.307298
2025-06-27 20:15:25,364 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:25.364 [时间戳:1751026525364], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.94, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 23
2025-06-27 20:15:25,904 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=754，卖单数=527
2025-06-27 20:15:25,905 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:25.905 [时间戳:1751026525905], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.94, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 24
2025-06-27 20:15:26,328 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:26,333 - __main__ - INFO - 大量深度更新: btcusdt 有 67 个买单和 51 个卖单更新, ID从 7895641774578 更新到 7895641781543
2025-06-27 20:15:26,333 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.158000, 当前时间戳: 2025-06-27 20:15:26.333573, 时间差: 0:01:15.175573
2025-06-27 20:15:26,413 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=756，卖单数=530
2025-06-27 20:15:26,492 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:26.492 [时间戳:1751026526492], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.00, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 25
2025-06-27 20:15:26,985 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=756，卖单数=530
2025-06-27 20:15:26,985 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:26,986 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:26.986 [时间戳:1751026526986], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.00, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 26
2025-06-27 20:15:26,989 - __main__ - INFO - 大量深度更新: btcusdt 有 75 个买单和 38 个卖单更新, ID从 7895641781543 更新到 7895641795189
2025-06-27 20:15:26,990 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.259000, 当前时间戳: 2025-06-27 20:15:26.990349, 时间差: 0:01:15.731349
2025-06-27 20:15:27,485 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=758，卖单数=533
2025-06-27 20:15:27,486 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:27.486 [时间戳:1751026527486], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.23, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 27
2025-06-27 20:15:27,998 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=758，卖单数=533
2025-06-27 20:15:27,998 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:28,034 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:28.034 [时间戳:1751026528034], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.23, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 28
2025-06-27 20:15:28,034 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.361000, 当前时间戳: 2025-06-27 20:15:28.034915, 时间差: 0:01:16.673915
2025-06-27 20:15:28,763 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=760，卖单数=539
2025-06-27 20:15:28,764 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:28.764 [时间戳:1751026528764], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.50, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 29
2025-06-27 20:15:28,906 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:28,911 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.464000, 当前时间戳: 2025-06-27 20:15:28.911045, 时间差: 0:01:17.447045
2025-06-27 20:15:29,318 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=762，卖单数=540
2025-06-27 20:15:29,399 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:29.399 [时间戳:1751026529399], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 23.02, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 30
2025-06-27 20:15:30,220 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=762，卖单数=540
2025-06-27 20:15:30,221 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:30,221 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:30.221 [时间戳:1751026530221], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 23.02, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 31
2025-06-27 20:15:30,226 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.569000, 当前时间戳: 2025-06-27 20:15:30.226352, 时间差: 0:01:18.657352
2025-06-27 20:15:30,733 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=763，卖单数=544
2025-06-27 20:15:30,734 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:30.734 [时间戳:1751026530734], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 22.93, 卖盘总量: 29.79, 点差: 0.0001%, 当前深度快照数量: 32
2025-06-27 20:15:30,901 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:30,906 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.001, 时间: 1751026451498
2025-06-27 20:15:31,317 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=772，卖单数=555
2025-06-27 20:15:31,410 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:31.410 [时间戳:1751026531410], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.38, 卖盘总量: 29.78, 点差: 0.0001%, 当前深度快照数量: 33
2025-06-27 20:15:31,930 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=772，卖单数=555
2025-06-27 20:15:31,931 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.672000, 当前时间戳: 2025-06-27 20:15:31.931448, 时间差: 0:01:20.259448
2025-06-27 20:15:31,931 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:31.931 [时间戳:1751026531931], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.38, 卖盘总量: 29.78, 点差: 0.0001%, 当前深度快照数量: 34
2025-06-27 20:15:32,802 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=772，卖单数=555
2025-06-27 20:15:32,802 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:32,803 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:32.803 [时间戳:1751026532803], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.38, 卖盘总量: 29.78, 点差: 0.0001%, 当前深度快照数量: 35
2025-06-27 20:15:32,808 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.778000, 当前时间戳: 2025-06-27 20:15:32.808045, 时间差: 0:01:21.030045
2025-06-27 20:15:33,342 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=776，卖单数=558
2025-06-27 20:15:33,343 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:33.343 [时间戳:1751026533343], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.89, 卖盘总量: 29.66, 点差: 0.0001%, 当前深度快照数量: 36
2025-06-27 20:15:33,364 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 36
2025-06-27 20:15:33,364 - __main__ - INFO - 准备保存 36 条快照数据，并已清空全局快照列表
2025-06-27 20:15:33,600 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:33,606 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.879000, 当前时间戳: 2025-06-27 20:15:33.606704, 时间差: 0:01:21.727704
2025-06-27 20:15:33,893 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=781，卖单数=566
2025-06-27 20:15:33,894 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:33.894 [时间戳:1751026533894], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.76, 卖盘总量: 29.65, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 20:15:34,196 - __main__ - INFO - 成功保存 36 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:15:34,457 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=781，卖单数=566
2025-06-27 20:15:34,631 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:34.631 [时间戳:1751026534631], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.76, 卖盘总量: 29.65, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 20:15:34,878 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:34,878 - __main__ - INFO - 共保存了 36 条深度快照数据到MariaDB
2025-06-27 20:15:34,882 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.059, 时间: 1751026451809
2025-06-27 20:15:35,026 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=782，卖单数=571
2025-06-27 20:15:35,027 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:35.027 [时间戳:1751026535027], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.35, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 3
2025-06-27 20:15:35,748 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=782，卖单数=571
2025-06-27 20:15:35,748 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:11.980000, 当前时间戳: 2025-06-27 20:15:35.748686, 时间差: 0:01:23.768686
2025-06-27 20:15:35,749 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:35.749 [时间戳:1751026535749], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.35, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 4
2025-06-27 20:15:35,911 - __main__ - INFO - 快照调度状态：过去30秒新增了 49 个快照，当前总计 4 个快照
2025-06-27 20:15:36,581 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=782，卖单数=571
2025-06-27 20:15:36,582 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:36.582 [时间戳:1751026536582], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.35, 卖盘总量: 29.84, 点差: 0.0001%, 当前深度快照数量: 5
2025-06-27 20:15:37,069 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:37,073 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.093, 时间: 1751026451885
2025-06-27 20:15:37,157 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=790，卖单数=572
2025-06-27 20:15:37,240 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:37.240 [时间戳:1751026537240], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.51, 卖盘总量: 29.76, 点差: 0.0001%, 当前深度快照数量: 6
2025-06-27 20:15:38,059 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=790，卖单数=572
2025-06-27 20:15:38,059 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.081000, 当前时间戳: 2025-06-27 20:15:38.059700, 时间差: 0:01:25.978700
2025-06-27 20:15:38,060 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:38.060 [时间戳:1751026538060], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.51, 卖盘总量: 29.76, 点差: 0.0001%, 当前深度快照数量: 7
2025-06-27 20:15:38,592 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=790，卖单数=572
2025-06-27 20:15:38,593 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:38.593 [时间戳:1751026538593], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 26.51, 卖盘总量: 29.76, 点差: 0.0001%, 当前深度快照数量: 8
2025-06-27 20:15:38,771 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:38,776 - __main__ - INFO - 大量深度更新: btcusdt 有 58 个买单和 36 个卖单更新, ID从 7895641840678 更新到 7895641845996
2025-06-27 20:15:38,776 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.184000, 当前时间戳: 2025-06-27 20:15:38.776683, 时间差: 0:01:26.592683
2025-06-27 20:15:39,137 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=796，卖单数=577
2025-06-27 20:15:39,209 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:39.209 [时间戳:1751026539209], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.02, 卖盘总量: 29.62, 点差: 0.0001%, 当前深度快照数量: 9
2025-06-27 20:15:39,657 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=796，卖单数=577
2025-06-27 20:15:39,658 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:39,658 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:39.658 [时间戳:1751026539658], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.02, 卖盘总量: 29.62, 点差: 0.0001%, 当前深度快照数量: 10
2025-06-27 20:15:39,663 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.288000, 当前时间戳: 2025-06-27 20:15:39.663699, 时间差: 0:01:27.375699
2025-06-27 20:15:40,172 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=800，卖单数=584
2025-06-27 20:15:40,173 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:40.173 [时间戳:1751026540173], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.88, 卖盘总量: 29.70, 点差: 0.0001%, 当前深度快照数量: 11
2025-06-27 20:15:40,982 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=800，卖单数=584
2025-06-27 20:15:40,982 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:40,983 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:40.982 [时间戳:1751026540982], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.88, 卖盘总量: 29.70, 点差: 0.0001%, 当前深度快照数量: 12
2025-06-27 20:15:40,987 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.388000, 当前时间戳: 2025-06-27 20:15:40.987738, 时间差: 0:01:28.599738
2025-06-27 20:15:41,525 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=806，卖单数=583
2025-06-27 20:15:41,526 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:41.526 [时间戳:1751026541526], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.82, 卖盘总量: 29.63, 点差: 0.0001%, 当前深度快照数量: 13
2025-06-27 20:15:42,143 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=806，卖单数=583
2025-06-27 20:15:42,146 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:42.145 [时间戳:1751026542145], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.82, 卖盘总量: 29.63, 点差: 0.0001%, 当前深度快照数量: 14
2025-06-27 20:15:42,309 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:42,317 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.484000, 当前时间戳: 2025-06-27 20:15:42.317577, 时间差: 0:01:29.833577
2025-06-27 20:15:42,652 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=808，卖单数=585
2025-06-27 20:15:42,819 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:42.819 [时间戳:1751026542819], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.24, 卖盘总量: 29.77, 点差: 0.0001%, 当前深度快照数量: 15
2025-06-27 20:15:42,986 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:42,991 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.588000, 当前时间戳: 2025-06-27 20:15:42.991105, 时间差: 0:01:30.403105
2025-06-27 20:15:43,188 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=809，卖单数=586
2025-06-27 20:15:43,188 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:43.188 [时间戳:1751026543188], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.76, 卖盘总量: 29.62, 点差: 0.0001%, 当前深度快照数量: 16
2025-06-27 20:15:43,568 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:43,573 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.695000, 当前时间戳: 2025-06-27 20:15:43.573738, 时间差: 0:01:30.878738
2025-06-27 20:15:43,732 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=809，卖单数=585
2025-06-27 20:15:43,733 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:43.733 [时间戳:1751026543733], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.60, 卖盘总量: 29.62, 点差: 0.0001%, 当前深度快照数量: 17
2025-06-27 20:15:44,267 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=809，卖单数=585
2025-06-27 20:15:44,268 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:44.268 [时间戳:1751026544268], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 24.60, 卖盘总量: 29.62, 点差: 0.0001%, 当前深度快照数量: 18
2025-06-27 20:15:44,559 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:44,564 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.781000, 当前时间戳: 2025-06-27 20:15:44.564130, 时间差: 0:01:31.783130
2025-06-27 20:15:44,776 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=810，卖单数=586
2025-06-27 20:15:44,778 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:44.778 [时间戳:1751026544778], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 25.75, 卖盘总量: 29.62, 点差: 0.0001%, 当前深度快照数量: 19
2025-06-27 20:15:43,196 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:43,201 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.899000, 当前时间戳: 2025-06-27 20:15:43.201122, 时间差: 0:01:30.302122
2025-06-27 20:15:43,836 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:43,841 - __main__ - INFO - 大量深度更新: btcusdt 有 80 个买单和 64 个卖单更新, ID从 7895641889448 更新到 7895641903184
2025-06-27 20:15:43,842 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:12.999000, 当前时间戳: 2025-06-27 20:15:43.842401, 时间差: 0:01:30.843401
2025-06-27 20:15:44,422 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:44,426 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.101000, 当前时间戳: 2025-06-27 20:15:44.426879, 时间差: 0:01:31.325879
2025-06-27 20:15:45,086 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:45,090 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.202000, 当前时间戳: 2025-06-27 20:15:45.090840, 时间差: 0:01:31.888840
2025-06-27 20:15:45,307 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=828，卖单数=605
2025-06-27 20:15:45,308 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:45.308 [时间戳:1751026545308], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.57, 卖盘总量: 25.85, 点差: 0.0001%, 当前深度快照数量: 20
2025-06-27 20:15:45,747 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:45,751 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.304000, 当前时间戳: 2025-06-27 20:15:45.751512, 时间差: 0:01:32.447512
2025-06-27 20:15:45,833 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=833，卖单数=609
2025-06-27 20:15:45,914 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:45.914 [时间戳:1751026545914], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 30.25, 卖盘总量: 25.86, 点差: 0.0001%, 当前深度快照数量: 21
2025-06-27 20:15:46,398 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=833，卖单数=609
2025-06-27 20:15:46,398 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:46,399 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:46.399 [时间戳:1751026546399], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 30.25, 卖盘总量: 25.86, 点差: 0.0001%, 当前深度快照数量: 22
2025-06-27 20:15:46,403 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.409000, 当前时间戳: 2025-06-27 20:15:46.403081, 时间差: 0:01:32.994081
2025-06-27 20:15:46,988 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=832，卖单数=608
2025-06-27 20:15:47,132 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:47.132 [时间戳:1751026547132], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 30.25, 卖盘总量: 25.93, 点差: 0.0001%, 当前深度快照数量: 23
2025-06-27 20:15:47,906 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=832，卖单数=608
2025-06-27 20:15:47,906 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:47,906 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:47.906 [时间戳:1751026547906], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 30.25, 卖盘总量: 25.93, 点差: 0.0001%, 当前深度快照数量: 24
2025-06-27 20:15:47,911 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.511000, 当前时间戳: 2025-06-27 20:15:47.911496, 时间差: 0:01:34.400496
2025-06-27 20:15:48,458 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=836，卖单数=610
2025-06-27 20:15:48,459 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:48.459 [时间戳:1751026548459], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 30.25, 卖盘总量: 25.92, 点差: 0.0001%, 当前深度快照数量: 25
2025-06-27 20:15:48,575 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:48,580 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.601000, 当前时间戳: 2025-06-27 20:15:48.580628, 时间差: 0:01:34.979628
2025-06-27 20:15:48,971 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=836，卖单数=611
2025-06-27 20:15:49,052 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:49.052 [时间戳:1751026549052], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 30.45, 卖盘总量: 25.94, 点差: 0.0001%, 当前深度快照数量: 26
2025-06-27 20:15:49,560 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=836，卖单数=611
2025-06-27 20:15:49,560 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:49,561 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:49.561 [时间戳:1751026549561], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 30.45, 卖盘总量: 25.94, 点差: 0.0001%, 当前深度快照数量: 27
2025-06-27 20:15:49,566 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.50, 数量: 0.003, 时间: 1751026453460
2025-06-27 20:15:50,090 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=838，卖单数=612
2025-06-27 20:15:50,090 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:50.090 [时间戳:1751026550090], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.08, 卖盘总量: 25.94, 点差: 0.0001%, 当前深度快照数量: 28
2025-06-27 20:15:50,519 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.713000, 当前时间戳: 2025-06-27 20:15:50.519217, 时间差: 0:01:36.806217
2025-06-27 20:15:50,591 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=838，卖单数=612
2025-06-27 20:15:50,972 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:50.972 [时间戳:1751026550972], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.08, 卖盘总量: 25.94, 点差: 0.0001%, 当前深度快照数量: 29
2025-06-27 20:15:51,118 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=838，卖单数=612
2025-06-27 20:15:51,262 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:51.262 [时间戳:1751026551262], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.08, 卖盘总量: 25.94, 点差: 0.0001%, 当前深度快照数量: 30
2025-06-27 20:15:51,405 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:51,410 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.814000, 当前时间戳: 2025-06-27 20:15:51.410179, 时间差: 0:01:37.596179
2025-06-27 20:15:51,742 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=838，卖单数=615
2025-06-27 20:15:51,904 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:51.904 [时间戳:1751026551904], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 29.75, 卖盘总量: 25.86, 点差: 0.0001%, 当前深度快照数量: 31
2025-06-27 20:15:52,067 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:52,076 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:13.918000, 当前时间戳: 2025-06-27 20:15:52.076550, 时间差: 0:01:38.158550
2025-06-27 20:15:52,286 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=838，卖单数=618
2025-06-27 20:15:52,287 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:52.287 [时间戳:1751026552287], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 29.72, 卖盘总量: 25.86, 点差: 0.0001%, 当前深度快照数量: 32
2025-06-27 20:15:52,803 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=838，卖单数=618
2025-06-27 20:15:52,803 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:52,804 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:52.804 [时间戳:1751026552804], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 29.72, 卖盘总量: 25.86, 点差: 0.0001%, 当前深度快照数量: 33
2025-06-27 20:15:52,808 - __main__ - INFO - 大量深度更新: btcusdt 有 58 个买单和 80 个卖单更新, ID从 7895641942100 更新到 7895641959893
2025-06-27 20:15:52,808 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.105, 时间: 1751026453860
2025-06-27 20:15:53,305 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=837，卖单数=625
2025-06-27 20:15:53,305 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:53.305 [时间戳:1751026553305], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.80, 卖盘总量: 20.50, 点差: 0.0001%, 当前深度快照数量: 34
2025-06-27 20:15:53,478 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.021000, 当前时间戳: 2025-06-27 20:15:53.478515, 时间差: 0:01:39.457515
2025-06-27 20:15:53,945 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=837，卖单数=625
2025-06-27 20:15:53,946 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:53.946 [时间戳:1751026553946], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.80, 卖盘总量: 20.50, 点差: 0.0001%, 当前深度快照数量: 35
2025-06-27 20:15:54,707 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=837，卖单数=625
2025-06-27 20:15:54,708 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:54,708 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:54.708 [时间戳:1751026554708], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.80, 卖盘总量: 20.50, 点差: 0.0001%, 当前深度快照数量: 36
2025-06-27 20:15:54,713 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.125000, 当前时间戳: 2025-06-27 20:15:54.713111, 时间差: 0:01:40.588111
2025-06-27 20:15:55,214 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 36
2025-06-27 20:15:55,214 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 36，订单簿状态: 初始化=False，买单数=838，卖单数=634
2025-06-27 20:15:55,215 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:55.215 [时间戳:1751026555215], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.47, 卖盘总量: 21.37, 点差: 0.0001%, 当前深度快照数量: 37
2025-06-27 20:15:55,215 - __main__ - INFO - 准备保存 37 条快照数据，并已清空全局快照列表
2025-06-27 20:15:55,702 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:55,707 - __main__ - INFO - 大量深度更新: btcusdt 有 61 个买单和 58 个卖单更新, ID从 7895641964594 更新到 7895641970337
2025-06-27 20:15:55,708 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.226000, 当前时间戳: 2025-06-27 20:15:55.708050, 时间差: 0:01:41.482050
2025-06-27 20:15:56,135 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=842，卖单数=631
2025-06-27 20:15:56,221 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:56.221 [时间戳:1751026556221], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.89, 卖盘总量: 20.84, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 20:15:57,279 - __main__ - INFO - 成功保存 37 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:15:57,364 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=842，卖单数=631
2025-06-27 20:15:57,364 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:57,532 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:57.532 [时间戳:1751026557532], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.89, 卖盘总量: 20.84, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 20:15:57,537 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.144, 时间: 1751026454100
2025-06-27 20:15:57,775 - __main__ - INFO - 共保存了 37 条深度快照数据到MariaDB
2025-06-27 20:15:57,924 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=846，卖单数=634
2025-06-27 20:15:58,377 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:58.377 [时间戳:1751026558377], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.87, 卖盘总量: 21.50, 点差: 0.0001%, 当前深度快照数量: 3
2025-06-27 20:15:58,430 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=846，卖单数=634
2025-06-27 20:15:58,431 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:58.431 [时间戳:1751026558431], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.87, 卖盘总量: 21.50, 点差: 0.0001%, 当前深度快照数量: 4
2025-06-27 20:15:58,823 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.321000, 当前时间戳: 2025-06-27 20:15:58.823211, 时间差: 0:01:44.502211
2025-06-27 20:15:58,972 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=846，卖单数=634
2025-06-27 20:15:58,973 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:58.972 [时间戳:1751026558972], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.87, 卖盘总量: 21.50, 点差: 0.0001%, 当前深度快照数量: 5
2025-06-27 20:15:59,015 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: False, 缓存的深度更新: 0, 快照数量: 5
2025-06-27 20:15:59,516 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=846，卖单数=634
2025-06-27 20:15:59,517 - __main__ - INFO - 深度快照已生成: solusdt @ 20:15:59.517 [时间戳:1751026559517], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.87, 卖盘总量: 21.50, 点差: 0.0001%, 当前深度快照数量: 6
2025-06-27 20:15:59,773 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:15:59,777 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.424000, 当前时间戳: 2025-06-27 20:15:59.777554, 时间差: 0:01:45.353554
2025-06-27 20:16:00,110 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=848，卖单数=636
2025-06-27 20:16:00,951 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:00.950 [时间戳:1751026560950], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 33.12, 卖盘总量: 21.32, 点差: 0.0001%, 当前深度快照数量: 7
2025-06-27 20:16:01,004 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=848，卖单数=636
2025-06-27 20:16:01,005 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:01.005 [时间戳:1751026561005], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 33.12, 卖盘总量: 21.32, 点差: 0.0001%, 当前深度快照数量: 8
2025-06-27 20:16:01,113 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:01,118 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.529000, 当前时间戳: 2025-06-27 20:16:01.118264, 时间差: 0:01:46.589264
2025-06-27 20:16:01,534 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=848，卖单数=638
2025-06-27 20:16:01,620 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:01.620 [时间戳:1751026561620], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 33.48, 卖盘总量: 21.32, 点差: 0.0001%, 当前深度快照数量: 9
2025-06-27 20:16:01,792 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:01,797 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.626000, 当前时间戳: 2025-06-27 20:16:01.797111, 时间差: 0:01:47.171111
2025-06-27 20:16:02,099 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=852，卖单数=642
2025-06-27 20:16:02,246 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:02.246 [时间戳:1751026562246], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.85, 卖盘总量: 21.32, 点差: 0.0001%, 当前深度快照数量: 10
2025-06-27 20:16:02,395 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:02,400 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.736000, 当前时间戳: 2025-06-27 20:16:02.400018, 时间差: 0:01:47.664018
2025-06-27 20:16:02,609 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=852，卖单数=642
2025-06-27 20:16:02,610 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:02.610 [时间戳:1751026562610], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.51, 卖盘总量: 21.32, 点差: 0.0001%, 当前深度快照数量: 11
2025-06-27 20:16:03,351 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=852，卖单数=642
2025-06-27 20:16:03,351 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:03,352 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:03.352 [时间戳:1751026563352], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.51, 卖盘总量: 21.32, 点差: 0.0001%, 当前深度快照数量: 12
2025-06-27 20:16:03,356 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.838000, 当前时间戳: 2025-06-27 20:16:03.356579, 时间差: 0:01:48.518579
2025-06-27 20:16:03,855 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=855，卖单数=643
2025-06-27 20:16:03,855 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:03.855 [时间戳:1751026563855], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.51, 卖盘总量: 22.28, 点差: 0.0001%, 当前深度快照数量: 13
2025-06-27 20:16:04,019 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:04,024 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:14.941000, 当前时间戳: 2025-06-27 20:16:04.024087, 时间差: 0:01:49.083087
2025-06-27 20:16:04,367 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=858，卖单数=641
2025-06-27 20:16:04,531 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:04.531 [时间戳:1751026564531], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.93, 卖盘总量: 21.31, 点差: 0.0001%, 当前深度快照数量: 14
2025-06-27 20:16:04,688 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:04,693 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.044000, 当前时间戳: 2025-06-27 20:16:04.693014, 时间差: 0:01:49.649014
2025-06-27 20:16:06,090 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=860，卖单数=644
2025-06-27 20:16:06,090 - __main__ - INFO - 数据库处理状态：已执行 0 次保存操作
2025-06-27 20:16:06,091 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:06.091 [时间戳:1751026566091], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.45, 卖盘总量: 21.25, 点差: 0.0001%, 当前深度快照数量: 15
2025-06-27 20:16:06,091 - __main__ - INFO - 快照调度状态：过去30秒新增了 48 个快照，当前总计 15 个快照
2025-06-27 20:16:06,608 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=860，卖单数=644
2025-06-27 20:16:06,684 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:06.684 [时间戳:1751026566684], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.45, 卖盘总量: 21.25, 点差: 0.0001%, 当前深度快照数量: 16
2025-06-27 20:16:06,827 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:06,832 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.004, 时间: 1751026454948
2025-06-27 20:16:07,159 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=861，卖单数=645
2025-06-27 20:16:07,322 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:07.322 [时间戳:1751026567322], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.71, 卖盘总量: 21.18, 点差: 0.0001%, 当前深度快照数量: 17
2025-06-27 20:16:07,816 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=861，卖单数=645
2025-06-27 20:16:07,817 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.147000, 当前时间戳: 2025-06-27 20:16:07.816979, 时间差: 0:01:52.669979
2025-06-27 20:16:07,817 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:07.817 [时间戳:1751026567817], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.71, 卖盘总量: 21.18, 点差: 0.0001%, 当前深度快照数量: 18
2025-06-27 20:16:08,368 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=861，卖单数=645
2025-06-27 20:16:08,369 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:08.369 [时间戳:1751026568369], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.71, 卖盘总量: 21.18, 点差: 0.0001%, 当前深度快照数量: 19
2025-06-27 20:16:08,478 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:08,483 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.250000, 当前时间戳: 2025-06-27 20:16:08.483560, 时间差: 0:01:53.233560
2025-06-27 20:16:08,916 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=862，卖单数=651
2025-06-27 20:16:09,001 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:09.001 [时间戳:1751026569001], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.15, 卖盘总量: 22.03, 点差: 0.0001%, 当前深度快照数量: 20
2025-06-27 20:16:09,165 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:09,170 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.352000, 当前时间戳: 2025-06-27 20:16:09.170247, 时间差: 0:01:53.818247
2025-06-27 20:16:09,423 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=861，卖单数=653
2025-06-27 20:16:09,424 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:09.424 [时间戳:1751026569424], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.30, 卖盘总量: 22.03, 点差: 0.0001%, 当前深度快照数量: 21
2025-06-27 20:16:10,082 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=861，卖单数=653
2025-06-27 20:16:10,227 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:10.227 [时间戳:1751026570227], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.30, 卖盘总量: 22.03, 点差: 0.0001%, 当前深度快照数量: 22
2025-06-27 20:16:10,371 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:10,375 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 0.005, 时间: 1751026455267
2025-06-27 20:16:10,592 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=865，卖单数=657
2025-06-27 20:16:10,593 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:10.592 [时间戳:1751026570592], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.09, 卖盘总量: 22.06, 点差: 0.0001%, 当前深度快照数量: 23
2025-06-27 20:16:11,162 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=865，卖单数=657
2025-06-27 20:16:11,163 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:11.163 [时间戳:1751026571163], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.09, 卖盘总量: 22.06, 点差: 0.0001%, 当前深度快照数量: 24
2025-06-27 20:16:11,330 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.443000, 当前时间戳: 2025-06-27 20:16:11.330942, 时间差: 0:01:55.887942
2025-06-27 20:16:12,000 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=865，卖单数=657
2025-06-27 20:16:12,169 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:12.168 [时间戳:1751026572168], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.09, 卖盘总量: 22.06, 点差: 0.0001%, 当前深度快照数量: 25
2025-06-27 20:16:12,340 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:12,349 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.558000, 当前时间戳: 2025-06-27 20:16:12.349833, 时间差: 0:01:56.791833
2025-06-27 20:16:12,523 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=868，卖单数=656
2025-06-27 20:16:12,525 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:12.525 [时间戳:1751026572525], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.09, 卖盘总量: 22.18, 点差: 0.0001%, 当前深度快照数量: 26
2025-06-27 20:16:13,190 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=868，卖单数=656
2025-06-27 20:16:13,191 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:13.191 [时间戳:1751026573191], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 31.09, 卖盘总量: 22.18, 点差: 0.0001%, 当前深度快照数量: 27
2025-06-27 20:16:13,671 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:13,676 - __main__ - INFO - 大量深度更新: btcusdt 有 79 个买单和 74 个卖单更新, ID从 7895642028981 更新到 7895642034712
2025-06-27 20:16:13,676 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.659000, 当前时间戳: 2025-06-27 20:16:13.676708, 时间差: 0:01:58.017708
2025-06-27 20:16:13,749 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=889，卖单数=684
2025-06-27 20:16:14,131 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:14.131 [时间戳:1751026574131], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.40, 卖盘总量: 20.41, 点差: 0.0001%, 当前深度快照数量: 28
2025-06-27 20:16:14,276 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=889，卖单数=684
2025-06-27 20:16:14,420 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:14.420 [时间戳:1751026574420], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.40, 卖盘总量: 20.41, 点差: 0.0001%, 当前深度快照数量: 29
2025-06-27 20:16:14,563 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:14,568 - __main__ - INFO - 大量深度更新: btcusdt 有 44 个买单和 53 个卖单更新, ID从 7895642034712 更新到 7895642038591
2025-06-27 20:16:14,568 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106918.60, 数量: 2.900, 时间: 1751026455543
2025-06-27 20:16:14,791 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=894，卖单数=688
2025-06-27 20:16:14,791 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:14.791 [时间戳:1751026574791], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.02, 卖盘总量: 18.17, 点差: 0.0001%, 当前深度快照数量: 30
2025-06-27 20:16:15,219 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.761000, 当前时间戳: 2025-06-27 20:16:15.219719, 时间差: 0:01:59.458719
2025-06-27 20:16:15,309 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=894，卖单数=688
2025-06-27 20:16:15,390 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:15.390 [时间戳:1751026575390], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.02, 卖盘总量: 18.17, 点差: 0.0001%, 当前深度快照数量: 31
2025-06-27 20:16:15,885 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=894，卖单数=688
2025-06-27 20:16:15,885 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:15,886 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:15.886 [时间戳:1751026575886], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.02, 卖盘总量: 18.17, 点差: 0.0001%, 当前深度快照数量: 32
2025-06-27 20:16:15,891 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:14:15.861000, 当前时间戳: 2025-06-27 20:16:15.891040, 时间差: 0:02:00.030040
2025-06-27 20:16:16,524 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=894，卖单数=692
2025-06-27 20:16:16,690 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:16.690 [时间戳:1751026576690], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.72, 卖盘总量: 19.24, 点差: 0.0001%, 当前深度快照数量: 33
2025-06-27 20:16:17,184 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=894，卖单数=692
2025-06-27 20:16:17,184 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:17,185 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:17.185 [时间戳:1751026577185], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 32.72, 卖盘总量: 19.24, 点差: 0.0001%, 当前深度快照数量: 34
2025-06-27 20:16:17,190 - __main__ - ERROR - WebSocket连接错误: ping/pong timed out
2025-06-27 20:16:17,191 - websocket - ERROR - ping/pong timed out - goodbye
2025-06-27 20:16:17,393 - __main__ - WARNING - WebSocket连接关闭: None None
2025-06-27 20:16:17,393 - __main__ - INFO - 触发WebSocket重连...
2025-06-27 20:16:17,394 - __main__ - INFO - 等待重连事件...
2025-06-27 20:16:17,394 - __main__ - INFO - 收到重连事件，准备重新连接...
2025-06-27 20:16:17,394 - __main__ - INFO - 正在创建WebSocket连接，准备订阅 BTCUSDT
2025-06-27 20:16:17,724 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=894，卖单数=691
2025-06-27 20:16:17,724 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:17.724 [时间戳:1751026577724], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 33.42, 卖盘总量: 18.67, 点差: 0.0001%, 当前深度快照数量: 35
2025-06-27 20:16:17,768 - websocket - INFO - Websocket connected
2025-06-27 20:16:17,769 - __main__ - INFO - 已连接到Binance WebSocket，准备订阅 BTCUSDT 数据
2025-06-27 20:16:17,769 - __main__ - INFO - 已发送 BTCUSDT 订阅请求: 深度更新和聚合交易
2025-06-27 20:16:17,770 - __main__ - INFO - 尝试初始化订单簿 (第 1/5 次)
2025-06-27 20:16:17,770 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:16:17,770 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:16:17,774 - __main__ - WARNING - 订单簿初始化失败，2秒后重试...
2025-06-27 20:16:18,002 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:15.602000, 当前时间戳: 2025-06-27 20:16:18.002385, 时间差: 0:00:02.400385
2025-06-27 20:16:18,201 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 35
2025-06-27 20:16:18,202 - __main__ - INFO - 准备保存 35 条快照数据，并已清空全局快照列表
2025-06-27 20:16:18,430 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=894，卖单数=691
2025-06-27 20:16:18,575 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:18.575 [时间戳:1751026578575], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 33.42, 卖盘总量: 18.67, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 20:16:18,843 - __main__ - INFO - 成功保存 35 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:16:19,386 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=894，卖单数=691
2025-06-27 20:16:19,461 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:19,461 - __main__ - INFO - 共保存了 35 条深度快照数据到MariaDB
2025-06-27 20:16:19,461 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:19.461 [时间戳:1751026579461], 买盘最高价: 106918.5, 卖盘最低价: 106918.6, 买盘总量: 33.42, 卖盘总量: 18.67, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 20:16:19,466 - __main__ - INFO - 大量深度更新: btcusdt 有 53 个买单和 24 个卖单更新, ID从 7895642045720 更新到 7895651188293
2025-06-27 20:16:19,467 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.050, 时间: 1751026575505
2025-06-27 20:16:20,306 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=913，卖单数=703
2025-06-27 20:16:20,306 - __main__ - INFO - 尝试初始化订单簿 (第 2/5 次)
2025-06-27 20:16:20,307 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:20.306 [时间戳:1751026580306], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 40.55, 卖盘总量: 42.84, 点差: -0.0034%, 当前深度快照数量: 3
2025-06-27 20:16:20,307 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:16:20,307 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:16:20,310 - __main__ - WARNING - 订单簿初始化失败，4秒后重试...
2025-06-27 20:16:20,786 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:15.718000, 当前时间戳: 2025-06-27 20:16:20.786246, 时间差: 0:00:05.068246
2025-06-27 20:16:20,871 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=913，卖单数=703
2025-06-27 20:16:21,292 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:21.292 [时间戳:1751026581292], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 40.55, 卖盘总量: 42.84, 点差: -0.0034%, 当前深度快照数量: 4
2025-06-27 20:16:21,398 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=913，卖单数=703
2025-06-27 20:16:21,399 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:21.399 [时间戳:1751026581399], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 40.55, 卖盘总量: 42.84, 点差: -0.0034%, 当前深度快照数量: 5
2025-06-27 20:16:21,955 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=913，卖单数=703
2025-06-27 20:16:21,956 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:21.956 [时间戳:1751026581956], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 40.55, 卖盘总量: 42.84, 点差: -0.0034%, 当前深度快照数量: 6
2025-06-27 20:16:22,121 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:22,125 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:15.821000, 当前时间戳: 2025-06-27 20:16:22.125540, 时间差: 0:00:06.304540
2025-06-27 20:16:22,480 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=921，卖单数=705
2025-06-27 20:16:22,481 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:22.481 [时间戳:1751026582481], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.89, 卖盘总量: 42.84, 点差: -0.0034%, 当前深度快照数量: 7
2025-06-27 20:16:23,489 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=921，卖单数=705
2025-06-27 20:16:23,490 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:23.490 [时间戳:1751026583490], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.89, 卖盘总量: 42.84, 点差: -0.0034%, 当前深度快照数量: 8
2025-06-27 20:16:23,928 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:23,933 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:15.924000, 当前时间戳: 2025-06-27 20:16:23.933558, 时间差: 0:00:08.009558
2025-06-27 20:16:24,013 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=927，卖单数=705
2025-06-27 20:16:24,094 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:24.094 [时间戳:1751026584094], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.89, 卖盘总量: 42.84, 点差: -0.0034%, 当前深度快照数量: 9
2025-06-27 20:16:24,409 - __main__ - INFO - 尝试初始化订单簿 (第 3/5 次)
2025-06-27 20:16:24,410 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:16:24,410 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:16:24,414 - __main__ - WARNING - 订单簿初始化失败，8秒后重试...
2025-06-27 20:16:24,570 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=927，卖单数=705
2025-06-27 20:16:24,570 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:24,578 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:24.578 [时间戳:1751026584578], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.89, 卖盘总量: 42.84, 点差: -0.0034%, 当前深度快照数量: 10
2025-06-27 20:16:24,586 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.026000, 当前时间戳: 2025-06-27 20:16:24.586961, 时间差: 0:00:08.560961
2025-06-27 20:16:25,402 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=931，卖单数=708
2025-06-27 20:16:25,403 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:25.403 [时间戳:1751026585403], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.06, 卖盘总量: 42.85, 点差: -0.0034%, 当前深度快照数量: 11
2025-06-27 20:16:25,576 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:25,581 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.003, 时间: 1751026575933
2025-06-27 20:16:26,081 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=940，卖单数=723
2025-06-27 20:16:26,081 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:26.081 [时间戳:1751026586081], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.29, 卖盘总量: 42.73, 点差: -0.0034%, 当前深度快照数量: 12
2025-06-27 20:16:26,576 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.126000, 当前时间戳: 2025-06-27 20:16:26.576312, 时间差: 0:00:10.450312
2025-06-27 20:16:26,645 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=940，卖单数=723
2025-06-27 20:16:26,997 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:26.997 [时间戳:1751026586997], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.29, 卖盘总量: 42.73, 点差: -0.0034%, 当前深度快照数量: 13
2025-06-27 20:16:27,209 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=940，卖单数=723
2025-06-27 20:16:27,280 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:27.280 [时间戳:1751026587280], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.29, 卖盘总量: 42.73, 点差: -0.0034%, 当前深度快照数量: 14
2025-06-27 20:16:27,716 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=940，卖单数=723
2025-06-27 20:16:27,716 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:27,717 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:27.717 [时间戳:1751026587717], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.29, 卖盘总量: 42.73, 点差: -0.0034%, 当前深度快照数量: 15
2025-06-27 20:16:27,721 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.229000, 当前时间戳: 2025-06-27 20:16:27.721074, 时间差: 0:00:11.492074
2025-06-27 20:16:28,233 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=941，卖单数=732
2025-06-27 20:16:28,234 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:28.234 [时间戳:1751026588234], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.03, 卖盘总量: 44.22, 点差: -0.0034%, 当前深度快照数量: 16
2025-06-27 20:16:28,754 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=941，卖单数=732
2025-06-27 20:16:28,755 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:28.755 [时间戳:1751026588755], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.03, 卖盘总量: 44.22, 点差: -0.0034%, 当前深度快照数量: 17
2025-06-27 20:16:29,272 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=941，卖单数=732
2025-06-27 20:16:29,272 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:29,273 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:29.273 [时间戳:1751026589273], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.03, 卖盘总量: 44.22, 点差: -0.0034%, 当前深度快照数量: 18
2025-06-27 20:16:29,277 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.331000, 当前时间戳: 2025-06-27 20:16:29.277640, 时间差: 0:00:12.946640
2025-06-27 20:16:29,805 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=941，卖单数=747
2025-06-27 20:16:29,806 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:29.806 [时间戳:1751026589806], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.09, 卖盘总量: 44.22, 点差: -0.0034%, 当前深度快照数量: 19
2025-06-27 20:16:30,319 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=941，卖单数=747
2025-06-27 20:16:30,320 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:30.320 [时间戳:1751026590320], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.09, 卖盘总量: 44.22, 点差: -0.0034%, 当前深度快照数量: 20
2025-06-27 20:16:27,620 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:27,624 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.434000, 当前时间戳: 2025-06-27 20:16:27.624814, 时间差: 0:00:11.190814
2025-06-27 20:16:28,574 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:28,578 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.536000, 当前时间戳: 2025-06-27 20:16:28.578635, 时间差: 0:00:12.042635
2025-06-27 20:16:29,741 - __main__ - INFO - 尝试初始化订单簿 (第 4/5 次)
2025-06-27 20:16:29,880 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:16:29,881 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:16:29,884 - __main__ - WARNING - 订单簿初始化失败，16秒后重试...
2025-06-27 20:16:30,017 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:30,021 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.638000, 当前时间戳: 2025-06-27 20:16:30.021432, 时间差: 0:00:13.383432
2025-06-27 20:16:30,647 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:30,651 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.741000, 当前时间戳: 2025-06-27 20:16:30.651600, 时间差: 0:00:13.910600
2025-06-27 20:16:31,122 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=966，卖单数=771
2025-06-27 20:16:31,123 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:31.123 [时间戳:1751026591123], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.48, 卖盘总量: 45.11, 点差: -0.0034%, 当前深度快照数量: 21
2025-06-27 20:16:31,617 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:31,623 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.840000, 当前时间戳: 2025-06-27 20:16:31.623334, 时间差: 0:00:14.783334
2025-06-27 20:16:31,698 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=968，卖单数=772
2025-06-27 20:16:31,775 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:31.775 [时间戳:1751026591775], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.18, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 22
2025-06-27 20:16:32,334 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=968，卖单数=772
2025-06-27 20:16:32,415 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:32.415 [时间戳:1751026592415], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.18, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 23
2025-06-27 20:16:32,573 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:32,577 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:16.941000, 当前时间戳: 2025-06-27 20:16:32.577768, 时间差: 0:00:15.636768
2025-06-27 20:16:32,852 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=973，卖单数=774
2025-06-27 20:16:32,989 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:32.989 [时间戳:1751026592989], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.18, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 24
2025-06-27 20:16:33,126 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:33,130 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.044000, 当前时间戳: 2025-06-27 20:16:33.130940, 时间差: 0:00:16.086940
2025-06-27 20:16:33,448 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=978，卖单数=777
2025-06-27 20:16:33,611 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:33.611 [时间戳:1751026593611], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.05, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 25
2025-06-27 20:16:33,770 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:33,775 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.144000, 当前时间戳: 2025-06-27 20:16:33.775720, 时间差: 0:00:16.631720
2025-06-27 20:16:33,988 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=982，卖单数=778
2025-06-27 20:16:33,989 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:33.989 [时间戳:1751026593989], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.28, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 26
2025-06-27 20:16:34,503 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=982，卖单数=778
2025-06-27 20:16:34,915 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:34.915 [时间戳:1751026594915], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.28, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 27
2025-06-27 20:16:35,087 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=982，卖单数=778
2025-06-27 20:16:35,088 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:35,089 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:35.089 [时间戳:1751026595089], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.28, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 28
2025-06-27 20:16:35,093 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.251000, 当前时间戳: 2025-06-27 20:16:35.093633, 时间差: 0:00:17.842633
2025-06-27 20:16:35,600 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=984，卖单数=780
2025-06-27 20:16:35,601 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:35.601 [时间戳:1751026595601], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.28, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 29
2025-06-27 20:16:36,180 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=984，卖单数=780
2025-06-27 20:16:36,260 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:36.260 [时间戳:1751026596260], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.28, 卖盘总量: 45.31, 点差: -0.0034%, 当前深度快照数量: 30
2025-06-27 20:16:36,260 - __main__ - INFO - 快照调度状态：过去30秒新增了 50 个快照，当前总计 30 个快照
2025-06-27 20:16:36,433 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:36,438 - __main__ - INFO - 大量深度更新: btcusdt 有 57 个买单和 35 个卖单更新, ID从 7895651258936 更新到 7895651265918
2025-06-27 20:16:36,439 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.348000, 当前时间戳: 2025-06-27 20:16:36.439150, 时间差: 0:00:19.091150
2025-06-27 20:16:36,885 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=991，卖单数=786
2025-06-27 20:16:36,885 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:36.885 [时间戳:1751026596885], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.73, 卖盘总量: 45.78, 点差: -0.0034%, 当前深度快照数量: 31
2025-06-27 20:16:37,635 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=991，卖单数=786
2025-06-27 20:16:37,635 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:37,636 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:37.636 [时间戳:1751026597636], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.73, 卖盘总量: 45.78, 点差: -0.0034%, 当前深度快照数量: 32
2025-06-27 20:16:37,640 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.450000, 当前时间戳: 2025-06-27 20:16:37.640326, 时间差: 0:00:20.190326
2025-06-27 20:16:38,182 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=994，卖单数=786
2025-06-27 20:16:38,183 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:38.183 [时间戳:1751026598183], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.19, 卖盘总量: 45.47, 点差: -0.0034%, 当前深度快照数量: 33
2025-06-27 20:16:38,290 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:38,294 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.005, 时间: 1751026577310
2025-06-27 20:16:38,715 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=995，卖单数=789
2025-06-27 20:16:38,794 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:38.794 [时间戳:1751026598794], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.66, 卖盘总量: 44.81, 点差: -0.0034%, 当前深度快照数量: 34
2025-06-27 20:16:38,956 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.555000, 当前时间戳: 2025-06-27 20:16:38.956320, 时间差: 0:00:21.401320
2025-06-27 20:16:39,450 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=995，卖单数=789
2025-06-27 20:16:39,451 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:39.451 [时间戳:1751026599451], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.66, 卖盘总量: 44.81, 点差: -0.0034%, 当前深度快照数量: 35
2025-06-27 20:16:39,945 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:39,950 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.658000, 当前时间戳: 2025-06-27 20:16:39.949990, 时间差: 0:00:22.291990
2025-06-27 20:16:40,021 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 35
2025-06-27 20:16:40,021 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=1001，卖单数=795
2025-06-27 20:16:40,094 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:40.094 [时间戳:1751026600094], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.66, 卖盘总量: 44.81, 点差: -0.0034%, 当前深度快照数量: 36
2025-06-27 20:16:40,095 - __main__ - INFO - 准备保存 36 条快照数据，并已清空全局快照列表
2025-06-27 20:16:40,579 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=1001，卖单数=795
2025-06-27 20:16:41,044 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:41.044 [时间戳:1751026601044], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.66, 卖盘总量: 44.81, 点差: -0.0034%, 当前深度快照数量: 1
2025-06-27 20:16:41,201 - __main__ - INFO - 成功保存 36 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:16:41,360 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=1001，卖单数=795
2025-06-27 20:16:41,432 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:41,433 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:41.433 [时间戳:1751026601433], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.66, 卖盘总量: 44.81, 点差: -0.0034%, 当前深度快照数量: 2
2025-06-27 20:16:41,451 - __main__ - INFO - 大量深度更新: btcusdt 有 89 个买单和 113 个卖单更新, ID从 7895651280222 更新到 7895651298339
2025-06-27 20:16:41,452 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.761000, 当前时间戳: 2025-06-27 20:16:41.452415, 时间差: 0:00:23.691415
2025-06-27 20:16:42,054 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=1013，卖单数=835
2025-06-27 20:16:42,055 - __main__ - INFO - 共保存了 36 条深度快照数据到MariaDB
2025-06-27 20:16:42,059 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:42.058 [时间戳:1751026602058], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.14, 卖盘总量: 36.66, 点差: -0.0034%, 当前深度快照数量: 3
2025-06-27 20:16:42,549 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:42,554 - __main__ - INFO - 大量深度更新: btcusdt 有 47 个买单和 55 个卖单更新, ID从 7895651298339 更新到 7895651321486
2025-06-27 20:16:42,557 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.861000, 当前时间戳: 2025-06-27 20:16:42.557010, 时间差: 0:00:24.696010
2025-06-27 20:16:42,641 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=1016，卖单数=844
2025-06-27 20:16:42,728 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:42.728 [时间戳:1751026602728], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.59, 卖盘总量: 35.94, 点差: -0.0034%, 当前深度快照数量: 4
2025-06-27 20:16:43,245 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=1016，卖单数=844
2025-06-27 20:16:43,245 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:43,246 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:43.246 [时间戳:1751026603246], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.59, 卖盘总量: 35.94, 点差: -0.0034%, 当前深度快照数量: 5
2025-06-27 20:16:43,250 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:17.964000, 当前时间戳: 2025-06-27 20:16:43.250788, 时间差: 0:00:25.286788
2025-06-27 20:16:43,749 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=1026，卖单数=852
2025-06-27 20:16:43,750 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:43.750 [时间戳:1751026603750], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.17, 卖盘总量: 36.55, 点差: -0.0034%, 当前深度快照数量: 6
2025-06-27 20:16:43,842 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:43,846 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.068000, 当前时间戳: 2025-06-27 20:16:43.846593, 时间差: 0:00:25.778593
2025-06-27 20:16:44,285 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=1031，卖单数=851
2025-06-27 20:16:44,286 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:44.286 [时间戳:1751026604286], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.17, 卖盘总量: 36.56, 点差: -0.0034%, 当前深度快照数量: 7
2025-06-27 20:16:44,866 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=1031，卖单数=851
2025-06-27 20:16:44,867 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:44,867 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:44.867 [时间戳:1751026604867], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.17, 卖盘总量: 36.56, 点差: -0.0034%, 当前深度快照数量: 8
2025-06-27 20:16:44,872 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.172000, 当前时间戳: 2025-06-27 20:16:44.872205, 时间差: 0:00:26.700205
2025-06-27 20:16:45,392 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=1035，卖单数=855
2025-06-27 20:16:45,393 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:45.393 [时间戳:1751026605393], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.20, 卖盘总量: 36.73, 点差: -0.0034%, 当前深度快照数量: 9
2025-06-27 20:16:46,202 - __main__ - INFO - 尝试初始化订单簿 (第 5/5 次)
2025-06-27 20:16:46,203 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=1035，卖单数=855
2025-06-27 20:16:46,203 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:46,203 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:16:46,204 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:46.204 [时间戳:1751026606204], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.20, 卖盘总量: 36.73, 点差: -0.0034%, 当前深度快照数量: 10
2025-06-27 20:16:46,208 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.274000, 当前时间戳: 2025-06-27 20:16:46.208118, 时间差: 0:00:27.934118
2025-06-27 20:16:46,208 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:16:46,735 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=1039，卖单数=860
2025-06-27 20:16:46,736 - __main__ - ERROR - 订单簿初始化失败，已达到最大重试次数 5
2025-06-27 20:16:46,736 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:46.736 [时间戳:1751026606736], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.74, 卖盘总量: 36.73, 点差: -0.0034%, 当前深度快照数量: 11
2025-06-27 20:16:46,736 - __main__ - ERROR - 所有初始化尝试均失败，可能需要检查API连接或限制
2025-06-27 20:16:47,315 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=1039，卖单数=860
2025-06-27 20:16:47,408 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:47.408 [时间戳:1751026607408], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.74, 卖盘总量: 36.73, 点差: -0.0034%, 当前深度快照数量: 12
2025-06-27 20:16:47,581 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:47,586 - __main__ - INFO - 大量深度更新: btcusdt 有 51 个买单和 46 个卖单更新, ID从 7895651346992 更新到 7895651353310
2025-06-27 20:16:47,587 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.378000, 当前时间戳: 2025-06-27 20:16:47.587018, 时间差: 0:00:29.209018
2025-06-27 20:16:47,884 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=1042，卖单数=859
2025-06-27 20:16:48,343 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:48.343 [时间戳:1751026608343], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.25, 卖盘总量: 33.34, 点差: -0.0034%, 当前深度快照数量: 13
2025-06-27 20:16:48,398 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=1042，卖单数=859
2025-06-27 20:16:48,398 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:48.398 [时间戳:1751026608398], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.25, 卖盘总量: 33.34, 点差: -0.0034%, 当前深度快照数量: 14
2025-06-27 20:16:48,493 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:48,498 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.481000, 当前时间戳: 2025-06-27 20:16:48.498786, 时间差: 0:00:30.017786
2025-06-27 20:16:48,910 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=1047，卖单数=859
2025-06-27 20:16:49,618 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:49.618 [时间戳:1751026609618], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.33, 卖盘总量: 33.34, 点差: -0.0034%, 当前深度快照数量: 15
2025-06-27 20:16:49,672 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=1047，卖单数=859
2025-06-27 20:16:49,673 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:49.673 [时间戳:1751026609673], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.33, 卖盘总量: 33.34, 点差: -0.0034%, 当前深度快照数量: 16
2025-06-27 20:16:50,429 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=1047，卖单数=859
2025-06-27 20:16:50,429 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:50,430 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:50.430 [时间戳:1751026610430], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.33, 卖盘总量: 33.34, 点差: -0.0034%, 当前深度快照数量: 17
2025-06-27 20:16:50,434 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.585000, 当前时间戳: 2025-06-27 20:16:50.434722, 时间差: 0:00:31.849722
2025-06-27 20:16:50,980 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=1051，卖单数=861
2025-06-27 20:16:50,981 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:50.981 [时间戳:1751026610981], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.28, 卖盘总量: 32.92, 点差: -0.0034%, 当前深度快照数量: 18
2025-06-27 20:16:51,410 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:51,415 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.046, 时间: 1751026578463
2025-06-27 20:16:51,498 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=1054，卖单数=863
2025-06-27 20:16:51,585 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:51.585 [时间戳:1751026611585], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.28, 卖盘总量: 32.80, 点差: -0.0034%, 当前深度快照数量: 19
2025-06-27 20:16:52,086 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=1054，卖单数=863
2025-06-27 20:16:52,163 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:52.163 [时间戳:1751026612163], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.28, 卖盘总量: 32.80, 点差: -0.0034%, 当前深度快照数量: 20
2025-06-27 20:16:52,621 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=1054，卖单数=863
2025-06-27 20:16:52,623 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:52.622 [时间戳:1751026612623], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.28, 卖盘总量: 32.80, 点差: -0.0034%, 当前深度快照数量: 21
2025-06-27 20:16:52,733 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.685000, 当前时间戳: 2025-06-27 20:16:52.733369, 时间差: 0:00:34.048369
2025-06-27 20:16:53,205 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=1054，卖单数=863
2025-06-27 20:16:53,206 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:53.206 [时间戳:1751026613206], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.28, 卖盘总量: 32.80, 点差: -0.0034%, 当前深度快照数量: 22
2025-06-27 20:16:53,716 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=1054，卖单数=863
2025-06-27 20:16:53,788 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:53.788 [时间戳:1751026613788], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.28, 卖盘总量: 32.80, 点差: -0.0034%, 当前深度快照数量: 23
2025-06-27 20:16:53,933 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:53,937 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.785000, 当前时间戳: 2025-06-27 20:16:53.937960, 时间差: 0:00:35.152960
2025-06-27 20:16:54,265 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=1058，卖单数=868
2025-06-27 20:16:54,420 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:54.420 [时间戳:1751026614420], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.57, 卖盘总量: 33.40, 点差: -0.0034%, 当前深度快照数量: 24
2025-06-27 20:16:54,582 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:54,587 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.881000, 当前时间戳: 2025-06-27 20:16:54.587177, 时间差: 0:00:35.706177
2025-06-27 20:16:55,093 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=1061，卖单数=870
2025-06-27 20:16:55,094 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:55.094 [时间戳:1751026615094], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.34, 卖盘总量: 32.95, 点差: -0.0034%, 当前深度快照数量: 25
2025-06-27 20:16:56,017 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=1061，卖单数=870
2025-06-27 20:16:56,103 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: False, 缓存的深度更新: 0, 快照数量: 25
2025-06-27 20:16:56,104 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:56.104 [时间戳:1751026616104], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.34, 卖盘总量: 32.95, 点差: -0.0034%, 当前深度快照数量: 26
2025-06-27 20:16:56,262 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:56,267 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:18.981000, 当前时间戳: 2025-06-27 20:16:56.267259, 时间差: 0:00:37.286259
2025-06-27 20:16:56,603 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=1061，卖单数=870
2025-06-27 20:16:56,764 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:56.764 [时间戳:1751026616764], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.62, 卖盘总量: 33.22, 点差: -0.0034%, 当前深度快照数量: 27
2025-06-27 20:16:56,933 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:56,938 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.091000, 当前时间戳: 2025-06-27 20:16:56.938607, 时间差: 0:00:37.847607
2025-06-27 20:16:57,134 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=1067，卖单数=873
2025-06-27 20:16:57,135 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:57.135 [时间戳:1751026617135], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.13, 卖盘总量: 33.22, 点差: -0.0034%, 当前深度快照数量: 28
2025-06-27 20:16:57,510 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:57,515 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.198000, 当前时间戳: 2025-06-27 20:16:57.515021, 时间差: 0:00:38.317021
2025-06-27 20:16:57,670 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=1072，卖单数=878
2025-06-27 20:16:57,671 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:57.671 [时间戳:1751026617671], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.84, 卖盘总量: 33.26, 点差: -0.0034%, 当前深度快照数量: 29
2025-06-27 20:16:58,241 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=1072，卖单数=878
2025-06-27 20:16:58,320 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:58.320 [时间戳:1751026618320], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.84, 卖盘总量: 33.26, 点差: -0.0034%, 当前深度快照数量: 30
2025-06-27 20:16:58,487 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:58,492 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.301000, 当前时间戳: 2025-06-27 20:16:58.492600, 时间差: 0:00:39.191600
2025-06-27 20:16:58,830 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=1077，卖单数=884
2025-06-27 20:16:58,993 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:58.993 [时间戳:1751026618993], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.62, 卖盘总量: 33.45, 点差: -0.0034%, 当前深度快照数量: 31
2025-06-27 20:16:59,161 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:59,166 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.404000, 当前时间戳: 2025-06-27 20:16:59.166445, 时间差: 0:00:39.762445
2025-06-27 20:16:59,380 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=1078，卖单数=893
2025-06-27 20:16:59,381 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:59.381 [时间戳:1751026619381], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.99, 卖盘总量: 33.46, 点差: -0.0034%, 当前深度快照数量: 32
2025-06-27 20:16:59,804 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:16:59,809 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.280, 时间: 1751026579459
2025-06-27 20:16:59,881 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=1080，卖单数=898
2025-06-27 20:16:59,952 - __main__ - INFO - 深度快照已生成: solusdt @ 20:16:59.952 [时间戳:1751026619952], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.24, 卖盘总量: 33.46, 点差: -0.0034%, 当前深度快照数量: 33
2025-06-27 20:17:00,709 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=1080，卖单数=898
2025-06-27 20:17:00,851 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:00.851 [时间戳:1751026620851], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.24, 卖盘总量: 33.46, 点差: -0.0034%, 当前深度快照数量: 34
2025-06-27 20:17:00,994 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.502000, 当前时间戳: 2025-06-27 20:17:00.994860, 时间差: 0:00:41.492860
2025-06-27 20:17:01,392 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=1080，卖单数=898
2025-06-27 20:17:01,474 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:01.474 [时间戳:1751026621474], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.24, 卖盘总量: 33.46, 点差: -0.0034%, 当前深度快照数量: 35
2025-06-27 20:17:01,970 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=1080，卖单数=898
2025-06-27 20:17:01,970 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:01,971 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:01.971 [时间戳:1751026621971], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.24, 卖盘总量: 33.46, 点差: -0.0034%, 当前深度快照数量: 36
2025-06-27 20:17:01,975 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.609000, 当前时间戳: 2025-06-27 20:17:01.975518, 时间差: 0:00:42.366518
2025-06-27 20:17:02,410 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 36
2025-06-27 20:17:02,410 - __main__ - INFO - 准备保存 36 条快照数据，并已清空全局快照列表
2025-06-27 20:17:02,492 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=1082，卖单数=905
2025-06-27 20:17:02,573 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:02.573 [时间戳:1751026622573], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.23, 卖盘总量: 33.73, 点差: -0.0034%, 当前深度快照数量: 1
2025-06-27 20:17:03,038 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=1082，卖单数=905
2025-06-27 20:17:03,120 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:03.120 [时间戳:1751026623120], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.23, 卖盘总量: 33.73, 点差: -0.0034%, 当前深度快照数量: 2
2025-06-27 20:17:03,503 - __main__ - INFO - 成功保存 36 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:17:03,662 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=1082，卖单数=905
2025-06-27 20:17:04,084 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:04,085 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:04.085 [时间戳:1751026624085], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.23, 卖盘总量: 33.73, 点差: -0.0034%, 当前深度快照数量: 3
2025-06-27 20:17:04,091 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.316, 时间: 1751026579470
2025-06-27 20:17:04,163 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=1085，卖单数=907
2025-06-27 20:17:04,314 - __main__ - INFO - 共保存了 36 条深度快照数据到MariaDB
2025-06-27 20:17:04,314 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:04.314 [时间戳:1751026624314], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.72, 卖盘总量: 32.86, 点差: -0.0034%, 当前深度快照数量: 4
2025-06-27 20:17:04,740 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=1085，卖单数=907
2025-06-27 20:17:04,741 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.710000, 当前时间戳: 2025-06-27 20:17:04.741077, 时间差: 0:00:45.031077
2025-06-27 20:17:04,741 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:04.741 [时间戳:1751026624741], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.72, 卖盘总量: 32.86, 点差: -0.0034%, 当前深度快照数量: 5
2025-06-27 20:17:05,255 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=1085，卖单数=907
2025-06-27 20:17:05,256 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:05.256 [时间戳:1751026625256], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.72, 卖盘总量: 32.86, 点差: -0.0034%, 当前深度快照数量: 6
2025-06-27 20:17:05,354 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:05,359 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.808000, 当前时间戳: 2025-06-27 20:17:05.359313, 时间差: 0:00:45.551313
2025-06-27 20:17:06,149 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=1087，卖单数=910
2025-06-27 20:17:06,150 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:06.150 [时间戳:1751026626150], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.10, 卖盘总量: 32.66, 点差: -0.0034%, 当前深度快照数量: 7
2025-06-27 20:17:06,202 - __main__ - INFO - 快照调度状态：过去30秒新增了 49 个快照，当前总计 7 个快照
2025-06-27 20:17:06,316 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:06,321 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:19.911000, 当前时间戳: 2025-06-27 20:17:06.321135, 时间差: 0:00:46.410135
2025-06-27 20:17:06,399 - __main__ - INFO - 数据库处理状态：已执行 0 次保存操作
2025-06-27 20:17:06,655 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=1093，卖单数=913
2025-06-27 20:17:06,822 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:06.822 [时间戳:1751026626822], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.15, 卖盘总量: 32.46, 点差: -0.0034%, 当前深度快照数量: 8
2025-06-27 20:17:06,992 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:06,997 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.187, 时间: 1751026579769
2025-06-27 20:17:07,193 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=1090，卖单数=912
2025-06-27 20:17:07,194 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:07.194 [时间戳:1751026627194], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.81, 卖盘总量: 33.11, 点差: -0.0034%, 当前深度快照数量: 9
2025-06-27 20:17:07,564 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.010000, 当前时间戳: 2025-06-27 20:17:07.564492, 时间差: 0:00:47.554492
2025-06-27 20:17:07,700 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=1090，卖单数=912
2025-06-27 20:17:07,701 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:07.701 [时间戳:1751026627701], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.81, 卖盘总量: 33.11, 点差: -0.0034%, 当前深度快照数量: 10
2025-06-27 20:17:08,207 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=1090，卖单数=912
2025-06-27 20:17:08,208 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:08.208 [时间戳:1751026628208], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.81, 卖盘总量: 33.11, 点差: -0.0034%, 当前深度快照数量: 11
2025-06-27 20:17:08,765 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=1090，卖单数=912
2025-06-27 20:17:08,765 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:08,766 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:08.766 [时间戳:1751026628766], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.81, 卖盘总量: 33.11, 点差: -0.0034%, 当前深度快照数量: 12
2025-06-27 20:17:08,769 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.006, 时间: 1751026579913
2025-06-27 20:17:09,514 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=1099，卖单数=918
2025-06-27 20:17:09,515 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:09.515 [时间戳:1751026629515], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.24, 卖盘总量: 32.62, 点差: -0.0034%, 当前深度快照数量: 13
2025-06-27 20:17:09,675 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.115000, 当前时间戳: 2025-06-27 20:17:09.675301, 时间差: 0:00:49.560301
2025-06-27 20:17:10,306 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=1099，卖单数=918
2025-06-27 20:17:10,478 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:10.478 [时间戳:1751026630478], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.24, 卖盘总量: 32.62, 点差: -0.0034%, 当前深度快照数量: 14
2025-06-27 20:17:10,642 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:10,646 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.218000, 当前时间戳: 2025-06-27 20:17:10.646846, 时间差: 0:00:50.428846
2025-06-27 20:17:10,836 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=1097，卖单数=918
2025-06-27 20:17:10,837 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:10.837 [时间戳:1751026630837], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.15, 卖盘总量: 32.51, 点差: -0.0034%, 当前深度快照数量: 15
2025-06-27 20:17:11,349 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=1097，卖单数=918
2025-06-27 20:17:11,350 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:11.350 [时间戳:1751026631350], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.15, 卖盘总量: 32.51, 点差: -0.0034%, 当前深度快照数量: 16
2025-06-27 20:17:11,487 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:11,492 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.003, 时间: 1751026580110
2025-06-27 20:17:11,898 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=1101，卖单数=923
2025-06-27 20:17:11,977 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:11.976 [时间戳:1751026631976], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.15, 卖盘总量: 32.51, 点差: -0.0034%, 当前深度快照数量: 17
2025-06-27 20:17:12,138 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.320000, 当前时间戳: 2025-06-27 20:17:12.138200, 时间差: 0:00:51.818200
2025-06-27 20:17:12,482 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=1101，卖单数=923
2025-06-27 20:17:12,653 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:12.653 [时间戳:1751026632653], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.15, 卖盘总量: 32.51, 点差: -0.0034%, 当前深度快照数量: 18
2025-06-27 20:17:12,813 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:12,819 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.010, 时间: 1751026580336
2025-06-27 20:17:12,985 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=1101，卖单数=926
2025-06-27 20:17:12,986 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:12.986 [时间戳:1751026632986], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.34, 卖盘总量: 32.64, 点差: -0.0034%, 当前深度快照数量: 19
2025-06-27 20:17:13,550 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=1101，卖单数=926
2025-06-27 20:17:13,635 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:13.634 [时间戳:1751026633635], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.34, 卖盘总量: 32.64, 点差: -0.0034%, 当前深度快照数量: 20
2025-06-27 20:17:13,794 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.515, 时间: 1751026580340
2025-06-27 20:17:14,073 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=1101，卖单数=926
2025-06-27 20:17:14,216 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:14.216 [时间戳:1751026634216], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.34, 卖盘总量: 32.64, 点差: -0.0034%, 当前深度快照数量: 21
2025-06-27 20:17:14,589 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=1101，卖单数=926
2025-06-27 20:17:14,590 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:14.590 [时间戳:1751026634590], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.34, 卖盘总量: 32.64, 点差: -0.0034%, 当前深度快照数量: 22
2025-06-27 20:17:14,666 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.421000, 当前时间戳: 2025-06-27 20:17:14.666161, 时间差: 0:00:54.245161
2025-06-27 20:17:12,963 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:12,967 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.009, 时间: 1751026580352
2025-06-27 20:17:14,231 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.521000, 当前时间戳: 2025-06-27 20:17:14.231904, 时间差: 0:00:53.710904
2025-06-27 20:17:14,866 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:14,870 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.013, 时间: 1751026580430
2025-06-27 20:17:15,157 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=1112，卖单数=942
2025-06-27 20:17:15,299 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:15.299 [时间戳:1751026635299], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.40, 卖盘总量: 30.89, 点差: -0.0034%, 当前深度快照数量: 23
2025-06-27 20:17:15,672 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=1112，卖单数=942
2025-06-27 20:17:15,672 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:15.672 [时间戳:1751026635672], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.40, 卖盘总量: 30.89, 点差: -0.0034%, 当前深度快照数量: 24
2025-06-27 20:17:15,761 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.623000, 当前时间戳: 2025-06-27 20:17:15.761549, 时间差: 0:00:55.138549
2025-06-27 20:17:16,243 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=1112，卖单数=942
2025-06-27 20:17:16,244 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:16.244 [时间戳:1751026636244], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.40, 卖盘总量: 30.89, 点差: -0.0034%, 当前深度快照数量: 25
2025-06-27 20:17:16,722 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:16,739 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.726000, 当前时间戳: 2025-06-27 20:17:16.739660, 时间差: 0:00:56.013660
2025-06-27 20:17:16,817 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=1111，卖单数=942
2025-06-27 20:17:16,896 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:16.896 [时间戳:1751026636896], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.83, 卖盘总量: 31.29, 点差: -0.0034%, 当前深度快照数量: 26
2025-06-27 20:17:17,536 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=1111，卖单数=942
2025-06-27 20:17:17,536 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:17.536 [时间戳:1751026637536], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.83, 卖盘总量: 31.29, 点差: -0.0034%, 当前深度快照数量: 27
2025-06-27 20:17:18,043 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=1111，卖单数=942
2025-06-27 20:17:18,043 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:18,044 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:18.044 [时间戳:1751026638044], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.83, 卖盘总量: 31.29, 点差: -0.0034%, 当前深度快照数量: 28
2025-06-27 20:17:18,047 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.832000, 当前时间戳: 2025-06-27 20:17:18.047918, 时间差: 0:00:57.215918
2025-06-27 20:17:18,589 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=1115，卖单数=941
2025-06-27 20:17:18,590 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:18.590 [时间戳:1751026638590], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.87, 卖盘总量: 31.39, 点差: -0.0034%, 当前深度快照数量: 29
2025-06-27 20:17:18,700 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:18,705 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:20.929000, 当前时间戳: 2025-06-27 20:17:18.705337, 时间差: 0:00:57.776337
2025-06-27 20:17:19,141 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=1118，卖单数=944
2025-06-27 20:17:19,142 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:19.142 [时间戳:1751026639142], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.14, 卖盘总量: 31.39, 点差: -0.0034%, 当前深度快照数量: 30
2025-06-27 20:17:19,601 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:19,606 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.035000, 当前时间戳: 2025-06-27 20:17:19.606179, 时间差: 0:00:58.571179
2025-06-27 20:17:19,683 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=1119，卖单数=948
2025-06-27 20:17:19,769 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:19.769 [时间戳:1751026639769], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.14, 卖盘总量: 31.39, 点差: -0.0034%, 当前深度快照数量: 31
2025-06-27 20:17:20,258 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=1119，卖单数=948
2025-06-27 20:17:20,258 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:20,259 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:20.259 [时间戳:1751026640259], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.14, 卖盘总量: 31.39, 点差: -0.0034%, 当前深度快照数量: 32
2025-06-27 20:17:20,263 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.136000, 当前时间戳: 2025-06-27 20:17:20.263459, 时间差: 0:00:59.127459
2025-06-27 20:17:20,789 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=1123，卖单数=947
2025-06-27 20:17:20,790 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:20.790 [时间戳:1751026640790], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.14, 卖盘总量: 31.41, 点差: -0.0034%, 当前深度快照数量: 33
2025-06-27 20:17:20,958 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:20,963 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.033, 时间: 1751026581069
2025-06-27 20:17:21,302 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=1126，卖单数=947
2025-06-27 20:17:21,468 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:21.468 [时间戳:1751026641468], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.92, 卖盘总量: 32.30, 点差: -0.0034%, 当前深度快照数量: 34
2025-06-27 20:17:21,839 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=1126，卖单数=947
2025-06-27 20:17:21,840 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:21.839 [时间戳:1751026641839], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.92, 卖盘总量: 32.30, 点差: -0.0034%, 当前深度快照数量: 35
2025-06-27 20:17:21,948 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.238000, 当前时间戳: 2025-06-27 20:17:21.948454, 时间差: 0:01:00.710454
2025-06-27 20:17:22,355 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=1126，卖单数=947
2025-06-27 20:17:22,356 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:22.356 [时间戳:1751026642356], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.92, 卖盘总量: 32.30, 点差: -0.0034%, 当前深度快照数量: 36
2025-06-27 20:17:22,817 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:22,821 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.012, 时间: 1751026581184
2025-06-27 20:17:22,897 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 36，订单簿状态: 初始化=False，买单数=1128，卖单数=946
2025-06-27 20:17:22,977 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:22.977 [时间戳:1751026642977], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.68, 卖盘总量: 33.14, 点差: -0.0034%, 当前深度快照数量: 37
2025-06-27 20:17:23,453 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 37，订单簿状态: 初始化=False，买单数=1128，卖单数=946
2025-06-27 20:17:23,609 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:23.609 [时间戳:1751026643609], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.68, 卖盘总量: 33.14, 点差: -0.0034%, 当前深度快照数量: 38
2025-06-27 20:17:23,767 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.338000, 当前时间戳: 2025-06-27 20:17:23.767843, 时间差: 0:01:02.429843
2025-06-27 20:17:23,993 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 38，订单簿状态: 初始化=False，买单数=1128，卖单数=946
2025-06-27 20:17:23,993 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:23.993 [时间戳:1751026643993], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.68, 卖盘总量: 33.14, 点差: -0.0034%, 当前深度快照数量: 39
2025-06-27 20:17:24,522 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 39，订单簿状态: 初始化=False，买单数=1128，卖单数=946
2025-06-27 20:17:24,523 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:24.523 [时间戳:1751026644523], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.68, 卖盘总量: 33.14, 点差: -0.0034%, 当前深度快照数量: 40
2025-06-27 20:17:25,108 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 40，订单簿状态: 初始化=False，买单数=1128，卖单数=946
2025-06-27 20:17:25,108 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 40
2025-06-27 20:17:25,261 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:25.260 [时间戳:1751026645260], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.68, 卖盘总量: 33.14, 点差: -0.0034%, 当前深度快照数量: 41
2025-06-27 20:17:25,261 - __main__ - INFO - 准备保存 41 条快照数据，并已清空全局快照列表
2025-06-27 20:17:25,518 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:25,523 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.442000, 当前时间戳: 2025-06-27 20:17:25.523111, 时间差: 0:01:04.081111
2025-06-27 20:17:25,668 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=1126，卖单数=946
2025-06-27 20:17:25,669 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:25.669 [时间戳:1751026645669], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.48, 卖盘总量: 33.14, 点差: -0.0034%, 当前深度快照数量: 1
2025-06-27 20:17:26,180 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=1126，卖单数=946
2025-06-27 20:17:26,181 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:26.181 [时间戳:1751026646181], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.48, 卖盘总量: 33.14, 点差: -0.0034%, 当前深度快照数量: 2
2025-06-27 20:17:26,407 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:26,412 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.550000, 当前时间戳: 2025-06-27 20:17:26.411974, 时间差: 0:01:04.861974
2025-06-27 20:17:26,727 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=1128，卖单数=951
2025-06-27 20:17:26,886 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:26.886 [时间戳:1751026646886], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.21, 卖盘总量: 33.14, 点差: -0.0034%, 当前深度快照数量: 3
2025-06-27 20:17:27,047 - __main__ - INFO - 成功保存 41 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:17:27,962 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:27,962 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=1128，卖单数=951
2025-06-27 20:17:27,966 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.655000, 当前时间戳: 2025-06-27 20:17:27.966808, 时间差: 0:01:06.311808
2025-06-27 20:17:27,967 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:27.967 [时间戳:1751026647967], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.74, 卖盘总量: 33.01, 点差: -0.0034%, 当前深度快照数量: 4
2025-06-27 20:17:28,233 - __main__ - INFO - 共保存了 41 条深度快照数据到MariaDB
2025-06-27 20:17:28,498 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=1134，卖单数=953
2025-06-27 20:17:28,580 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:28.580 [时间戳:1751026648580], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 32.74, 卖盘总量: 33.01, 点差: -0.0034%, 当前深度快照数量: 5
2025-06-27 20:17:28,738 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:28,743 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.758000, 当前时间戳: 2025-06-27 20:17:28.743025, 时间差: 0:01:06.985025
2025-06-27 20:17:29,037 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=1135，卖单数=954
2025-06-27 20:17:29,181 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:29.181 [时间戳:1751026649181], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.96, 卖盘总量: 31.68, 点差: -0.0034%, 当前深度快照数量: 6
2025-06-27 20:17:29,325 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:29,329 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.858000, 当前时间戳: 2025-06-27 20:17:29.329424, 时间差: 0:01:07.471424
2025-06-27 20:17:29,545 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=1138，卖单数=959
2025-06-27 20:17:29,545 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:29.545 [时间戳:1751026649545], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.95, 卖盘总量: 31.25, 点差: -0.0034%, 当前深度快照数量: 7
2025-06-27 20:17:30,001 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:30,005 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:21.961000, 当前时间戳: 2025-06-27 20:17:30.005886, 时间差: 0:01:08.044886
2025-06-27 20:17:30,089 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=1141，卖单数=959
2025-06-27 20:17:30,175 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:30.175 [时间戳:1751026650175], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.91, 卖盘总量: 31.26, 点差: -0.0034%, 当前深度快照数量: 8
2025-06-27 20:17:30,682 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=1141，卖单数=959
2025-06-27 20:17:30,763 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:30.763 [时间戳:1751026650763], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.91, 卖盘总量: 31.26, 点差: -0.0034%, 当前深度快照数量: 9
2025-06-27 20:17:31,010 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:31,014 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.064000, 当前时间戳: 2025-06-27 20:17:31.014715, 时间差: 0:01:08.950715
2025-06-27 20:17:31,183 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=1142，卖单数=962
2025-06-27 20:17:31,184 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:31.184 [时间戳:1751026651184], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.95, 卖盘总量: 31.26, 点差: -0.0034%, 当前深度快照数量: 10
2025-06-27 20:17:31,991 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=1142，卖单数=962
2025-06-27 20:17:31,991 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:31,992 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:31.992 [时间戳:1751026651992], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.95, 卖盘总量: 31.26, 点差: -0.0034%, 当前深度快照数量: 11
2025-06-27 20:17:31,996 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.162000, 当前时间戳: 2025-06-27 20:17:31.996923, 时间差: 0:01:09.834923
2025-06-27 20:17:32,698 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=1145，卖单数=964
2025-06-27 20:17:32,773 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:32.772 [时间戳:1751026652773], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.95, 卖盘总量: 32.20, 点差: -0.0034%, 当前深度快照数量: 12
2025-06-27 20:17:33,519 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=1145，卖单数=964
2025-06-27 20:17:33,519 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:33,520 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:33.520 [时间戳:1751026653520], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.95, 卖盘总量: 32.20, 点差: -0.0034%, 当前深度快照数量: 13
2025-06-27 20:17:33,525 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.264000, 当前时间戳: 2025-06-27 20:17:33.525413, 时间差: 0:01:11.261413
2025-06-27 20:17:34,588 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=1147，卖单数=967
2025-06-27 20:17:34,675 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:34.675 [时间戳:1751026654675], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.19, 卖盘总量: 32.20, 点差: -0.0034%, 当前深度快照数量: 14
2025-06-27 20:17:34,850 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:34,855 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.377000, 当前时间戳: 2025-06-27 20:17:34.855353, 时间差: 0:01:12.478353
2025-06-27 20:17:35,185 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=1156，卖单数=971
2025-06-27 20:17:35,351 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:35.351 [时间戳:1751026655351], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.42, 卖盘总量: 32.20, 点差: -0.0034%, 当前深度快照数量: 15
2025-06-27 20:17:35,517 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:35,523 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.480000, 当前时间戳: 2025-06-27 20:17:35.523070, 时间差: 0:01:13.043070
2025-06-27 20:17:35,738 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=1154，卖单数=967
2025-06-27 20:17:35,740 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:35.739 [时间戳:1751026655739], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.27, 卖盘总量: 28.86, 点差: -0.0034%, 当前深度快照数量: 16
2025-06-27 20:17:36,202 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:36,207 - __main__ - INFO - 大量深度更新: btcusdt 有 100 个买单和 146 个卖单更新, ID从 7895651648548 更新到 7895651673082
2025-06-27 20:17:36,208 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.581000, 当前时间戳: 2025-06-27 20:17:36.208202, 时间差: 0:01:13.627202
2025-06-27 20:17:36,281 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=1161，卖单数=983
2025-06-27 20:17:36,354 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:36.354 [时间戳:1751026656354], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.01, 卖盘总量: 18.90, 点差: -0.0034%, 当前深度快照数量: 17
2025-06-27 20:17:36,354 - __main__ - INFO - 快照调度状态：过去30秒新增了 51 个快照，当前总计 17 个快照
2025-06-27 20:17:36,801 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=1161，卖单数=983
2025-06-27 20:17:36,802 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:36.802 [时间戳:1751026656802], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.01, 卖盘总量: 18.90, 点差: -0.0034%, 当前深度快照数量: 18
2025-06-27 20:17:37,080 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:37,085 - __main__ - INFO - 大量深度更新: btcusdt 有 60 个买单和 65 个卖单更新, ID从 7895651673082 更新到 7895651683601
2025-06-27 20:17:37,086 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.683000, 当前时间戳: 2025-06-27 20:17:37.086069, 时间差: 0:01:14.403069
2025-06-27 20:17:37,414 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=1169，卖单数=990
2025-06-27 20:17:37,573 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:37.573 [时间戳:1751026657573], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.98, 卖盘总量: 18.91, 点差: -0.0034%, 当前深度快照数量: 19
2025-06-27 20:17:37,725 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:37,729 - __main__ - INFO - 大量深度更新: btcusdt 有 68 个买单和 68 个卖单更新, ID从 7895651683601 更新到 7895651693758
2025-06-27 20:17:37,730 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.785000, 当前时间戳: 2025-06-27 20:17:37.730511, 时间差: 0:01:14.945511
2025-06-27 20:17:37,953 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=1175，卖单数=985
2025-06-27 20:17:37,954 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:37.954 [时间戳:1751026657954], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.49, 卖盘总量: 21.86, 点差: -0.0034%, 当前深度快照数量: 20
2025-06-27 20:17:38,401 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:38,405 - __main__ - INFO - 大量深度更新: btcusdt 有 61 个买单和 41 个卖单更新, ID从 7895651693758 更新到 7895651701846
2025-06-27 20:17:38,405 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.003, 时间: 1751026582744
2025-06-27 20:17:38,490 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=1176，卖单数=985
2025-06-27 20:17:38,574 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:38.574 [时间戳:1751026658574], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.68, 卖盘总量: 22.56, 点差: -0.0034%, 当前深度快照数量: 21
2025-06-27 20:17:39,062 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=1176，卖单数=985
2025-06-27 20:17:39,062 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.887000, 当前时间戳: 2025-06-27 20:17:39.062518, 时间差: 0:01:16.175518
2025-06-27 20:17:39,063 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:39.063 [时间戳:1751026659063], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.68, 卖盘总量: 22.56, 点差: -0.0034%, 当前深度快照数量: 22
2025-06-27 20:17:39,870 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=1176，卖单数=985
2025-06-27 20:17:39,871 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:39.871 [时间戳:1751026659871], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.68, 卖盘总量: 22.56, 点差: -0.0034%, 当前深度快照数量: 23
2025-06-27 20:17:40,248 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:40,252 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:22.993000, 当前时间戳: 2025-06-27 20:17:40.252926, 时间差: 0:01:17.259926
2025-06-27 20:17:40,412 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=1183，卖单数=990
2025-06-27 20:17:40,413 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:40.413 [时间戳:1751026660413], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.97, 卖盘总量: 21.57, 点差: -0.0034%, 当前深度快照数量: 24
2025-06-27 20:17:40,984 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=1183，卖单数=990
2025-06-27 20:17:41,070 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:41.070 [时间戳:1751026661070], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.97, 卖盘总量: 21.57, 点差: -0.0034%, 当前深度快照数量: 25
2025-06-27 20:17:41,244 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:41,249 - __main__ - INFO - 大量深度更新: btcusdt 有 53 个买单和 48 个卖单更新, ID从 7895651707138 更新到 7895651712719
2025-06-27 20:17:41,249 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.002, 时间: 1751026582880
2025-06-27 20:17:41,582 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=1181，卖单数=995
2025-06-27 20:17:41,787 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:41.748 [时间戳:1751026661773], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.13, 卖盘总量: 22.66, 点差: -0.0034%, 当前深度快照数量: 26
2025-06-27 20:17:41,937 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.091000, 当前时间戳: 2025-06-27 20:17:41.937083, 时间差: 0:01:18.846083
2025-06-27 20:17:42,111 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=1181，卖单数=995
2025-06-27 20:17:42,113 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:42.113 [时间戳:1751026662113], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.13, 卖盘总量: 22.66, 点差: -0.0034%, 当前深度快照数量: 27
2025-06-27 20:17:43,073 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=1181，卖单数=995
2025-06-27 20:17:43,074 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:43.074 [时间戳:1751026663074], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.13, 卖盘总量: 22.66, 点差: -0.0034%, 当前深度快照数量: 28
2025-06-27 20:17:43,256 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:43,261 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.196000, 当前时间戳: 2025-06-27 20:17:43.261635, 时间差: 0:01:20.065635
2025-06-27 20:17:43,623 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=1188，卖单数=1000
2025-06-27 20:17:43,695 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:43.695 [时间戳:1751026663695], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.39, 卖盘总量: 22.61, 点差: -0.0034%, 当前深度快照数量: 29
2025-06-27 20:17:44,150 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=1188，卖单数=1000
2025-06-27 20:17:44,150 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:44,151 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:44.151 [时间戳:1751026664151], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.39, 卖盘总量: 22.61, 点差: -0.0034%, 当前深度快照数量: 30
2025-06-27 20:17:44,155 - __main__ - INFO - 大量深度更新: btcusdt 有 73 个买单和 104 个卖单更新, ID从 7895651718829 更新到 7895651736909
2025-06-27 20:17:44,156 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.001, 时间: 1751026583110
2025-06-27 20:17:44,685 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=1194，卖单数=1008
2025-06-27 20:17:44,686 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:44.686 [时间戳:1751026664686], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.68, 卖盘总量: 18.99, 点差: -0.0034%, 当前深度快照数量: 31
2025-06-27 20:17:43,047 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.297000, 当前时间戳: 2025-06-27 20:17:43.047691, 时间差: 0:01:19.750691
2025-06-27 20:17:44,036 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:44,041 - __main__ - INFO - 大量深度更新: btcusdt 有 51 个买单和 51 个卖单更新, ID从 7895651736909 更新到 7895651745019
2025-06-27 20:17:44,041 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.399000, 当前时间戳: 2025-06-27 20:17:44.041858, 时间差: 0:01:20.642858
2025-06-27 20:17:45,226 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=1198，卖单数=1017
2025-06-27 20:17:45,227 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:45.227 [时间戳:1751026665227], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.53, 卖盘总量: 19.49, 点差: -0.0034%, 当前深度快照数量: 32
2025-06-27 20:17:45,400 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:45,404 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.501000, 当前时间戳: 2025-06-27 20:17:45.404934, 时间差: 0:01:21.903934
2025-06-27 20:17:45,760 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=1210，卖单数=1027
2025-06-27 20:17:45,761 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:45.761 [时间戳:1751026665761], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.55, 卖盘总量: 19.46, 点差: -0.0034%, 当前深度快照数量: 33
2025-06-27 20:17:46,401 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=1210，卖单数=1027
2025-06-27 20:17:46,474 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:46.474 [时间戳:1751026666474], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.55, 卖盘总量: 19.46, 点差: -0.0034%, 当前深度快照数量: 34
2025-06-27 20:17:46,916 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=1210，卖单数=1027
2025-06-27 20:17:46,916 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:46,924 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:46.924 [时间戳:1751026666924], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.55, 卖盘总量: 19.46, 点差: -0.0034%, 当前深度快照数量: 35
2025-06-27 20:17:46,929 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.601000, 当前时间戳: 2025-06-27 20:17:46.929819, 时间差: 0:01:23.328819
2025-06-27 20:17:47,421 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=1213，卖单数=1025
2025-06-27 20:17:47,422 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:47.422 [时间戳:1751026667422], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.74, 卖盘总量: 19.46, 点差: -0.0034%, 当前深度快照数量: 36
2025-06-27 20:17:47,581 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:47,586 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.705000, 当前时间戳: 2025-06-27 20:17:47.586189, 时间差: 0:01:23.881189
2025-06-27 20:17:47,940 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 36，订单簿状态: 初始化=False，买单数=1217，卖单数=1032
2025-06-27 20:17:48,113 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:48.113 [时间戳:1751026668113], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.78, 卖盘总量: 20.13, 点差: -0.0034%, 当前深度快照数量: 37
2025-06-27 20:17:48,281 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:48,286 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.808000, 当前时间戳: 2025-06-27 20:17:48.286074, 时间差: 0:01:24.478074
2025-06-27 20:17:48,449 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 37
2025-06-27 20:17:48,450 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 37，订单簿状态: 初始化=False，买单数=1217，卖单数=1032
2025-06-27 20:17:48,450 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:48.450 [时间戳:1751026668450], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.18, 卖盘总量: 20.18, 点差: -0.0034%, 当前深度快照数量: 38
2025-06-27 20:17:48,451 - __main__ - INFO - 准备保存 38 条快照数据，并已清空全局快照列表
2025-06-27 20:17:48,958 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=1217，卖单数=1032
2025-06-27 20:17:49,031 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:49.031 [时间戳:1751026669031], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.18, 卖盘总量: 20.18, 点差: -0.0034%, 当前深度快照数量: 1
2025-06-27 20:17:49,031 - __main__ - INFO - 成功保存 38 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:17:49,494 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=1217，卖单数=1032
2025-06-27 20:17:49,494 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:49,494 - __main__ - INFO - 共保存了 38 条深度快照数据到MariaDB
2025-06-27 20:17:49,495 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:49.495 [时间戳:1751026669495], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 38.18, 卖盘总量: 20.18, 点差: -0.0034%, 当前深度快照数量: 2
2025-06-27 20:17:49,499 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:23.904000, 当前时间戳: 2025-06-27 20:17:49.499140, 时间差: 0:01:25.595140
2025-06-27 20:17:50,036 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=1222，卖单数=1031
2025-06-27 20:17:50,037 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:50.037 [时间戳:1751026670037], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 37.10, 卖盘总量: 19.73, 点差: -0.0034%, 当前深度快照数量: 3
2025-06-27 20:17:50,151 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:50,156 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.010000, 当前时间戳: 2025-06-27 20:17:50.156462, 时间差: 0:01:26.146462
2025-06-27 20:17:51,223 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=1225，卖单数=1035
2025-06-27 20:17:51,307 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:51.306 [时间戳:1751026671306], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.35, 卖盘总量: 20.88, 点差: -0.0034%, 当前深度快照数量: 4
2025-06-27 20:17:51,349 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: False, 缓存的深度更新: 0, 快照数量: 4
2025-06-27 20:17:51,782 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=1225，卖单数=1035
2025-06-27 20:17:51,782 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:51,783 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:51.783 [时间戳:1751026671783], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.35, 卖盘总量: 20.88, 点差: -0.0034%, 当前深度快照数量: 5
2025-06-27 20:17:51,787 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.113000, 当前时间戳: 2025-06-27 20:17:51.787438, 时间差: 0:01:27.674438
2025-06-27 20:17:52,632 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=1233，卖单数=1039
2025-06-27 20:17:52,633 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:52.633 [时间戳:1751026672633], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.78, 卖盘总量: 20.96, 点差: -0.0034%, 当前深度快照数量: 6
2025-06-27 20:17:53,201 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=1233，卖单数=1039
2025-06-27 20:17:53,284 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:53.284 [时间戳:1751026673284], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.78, 卖盘总量: 20.96, 点差: -0.0034%, 当前深度快照数量: 7
2025-06-27 20:17:53,441 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:53,446 - __main__ - INFO - 大量深度更新: btcusdt 有 57 个买单和 48 个卖单更新, ID从 7895651789245 更新到 7895651802418
2025-06-27 20:17:53,446 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.215000, 当前时间戳: 2025-06-27 20:17:53.446948, 时间差: 0:01:29.231948
2025-06-27 20:17:53,908 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=1238，卖单数=1042
2025-06-27 20:17:53,909 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:53.909 [时间戳:1751026673909], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.72, 卖盘总量: 24.21, 点差: -0.0034%, 当前深度快照数量: 8
2025-06-27 20:17:54,422 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=1238，卖单数=1042
2025-06-27 20:17:54,796 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:54.796 [时间戳:1751026674796], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.72, 卖盘总量: 24.21, 点差: -0.0034%, 当前深度快照数量: 9
2025-06-27 20:17:54,938 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=1238，卖单数=1042
2025-06-27 20:17:54,938 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:54,939 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:54.939 [时间戳:1751026674939], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.72, 卖盘总量: 24.21, 点差: -0.0034%, 当前深度快照数量: 10
2025-06-27 20:17:54,943 - __main__ - INFO - 大量深度更新: btcusdt 有 90 个买单和 29 个卖单更新, ID从 7895651802418 更新到 7895651818531
2025-06-27 20:17:54,944 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.319000, 当前时间戳: 2025-06-27 20:17:54.944428, 时间差: 0:01:30.625428
2025-06-27 20:17:55,660 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=1269，卖单数=1045
2025-06-27 20:17:55,742 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:55.742 [时间戳:1751026675742], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.22, 卖盘总量: 25.31, 点差: -0.0034%, 当前深度快照数量: 11
2025-06-27 20:17:56,220 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=1269，卖单数=1045
2025-06-27 20:17:56,220 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:56,221 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:56.221 [时间戳:1751026676221], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.22, 卖盘总量: 25.31, 点差: -0.0034%, 当前深度快照数量: 12
2025-06-27 20:17:56,226 - __main__ - INFO - 大量深度更新: btcusdt 有 60 个买单和 59 个卖单更新, ID从 7895651818531 更新到 7895651825349
2025-06-27 20:17:56,226 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.422000, 当前时间戳: 2025-06-27 20:17:56.226717, 时间差: 0:01:31.804717
2025-06-27 20:17:56,730 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=1275，卖单数=1049
2025-06-27 20:17:56,731 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:56.731 [时间戳:1751026676731], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.42, 卖盘总量: 25.18, 点差: -0.0034%, 当前深度快照数量: 13
2025-06-27 20:17:57,285 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=1275，卖单数=1049
2025-06-27 20:17:57,373 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:57.373 [时间戳:1751026677373], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.42, 卖盘总量: 25.18, 点差: -0.0034%, 当前深度快照数量: 14
2025-06-27 20:17:57,540 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:57,545 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.522000, 当前时间戳: 2025-06-27 20:17:57.545431, 时间差: 0:01:33.023431
2025-06-27 20:17:57,876 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=1278，卖单数=1046
2025-06-27 20:17:58,037 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:58.037 [时间戳:1751026678037], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.72, 卖盘总量: 25.18, 点差: -0.0034%, 当前深度快照数量: 15
2025-06-27 20:17:58,201 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:58,205 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.623000, 当前时间戳: 2025-06-27 20:17:58.205566, 时间差: 0:01:33.582566
2025-06-27 20:17:58,404 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=1283，卖单数=1049
2025-06-27 20:17:58,405 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:58.405 [时间戳:1751026678405], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.72, 卖盘总量: 25.18, 点差: -0.0034%, 当前深度快照数量: 16
2025-06-27 20:17:58,942 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=1283，卖单数=1049
2025-06-27 20:17:58,943 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:58.943 [时间戳:1751026678943], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.72, 卖盘总量: 25.18, 点差: -0.0034%, 当前深度快照数量: 17
2025-06-27 20:17:59,092 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:17:59,096 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.730000, 当前时间戳: 2025-06-27 20:17:59.096881, 时间差: 0:01:34.366881
2025-06-27 20:17:59,502 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=1285，卖单数=1052
2025-06-27 20:17:59,587 - __main__ - INFO - 深度快照已生成: solusdt @ 20:17:59.587 [时间戳:1751026679587], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.37, 卖盘总量: 25.19, 点差: -0.0034%, 当前深度快照数量: 18
2025-06-27 20:18:00,149 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=1285，卖单数=1052
2025-06-27 20:18:00,226 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:00.226 [时间戳:1751026680226], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.37, 卖盘总量: 25.19, 点差: -0.0034%, 当前深度快照数量: 19
2025-06-27 20:18:01,013 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=1285，卖单数=1052
2025-06-27 20:18:01,013 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:01,014 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:01.014 [时间戳:1751026681014], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.37, 卖盘总量: 25.19, 点差: -0.0034%, 当前深度快照数量: 20
2025-06-27 20:18:01,018 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.827000, 当前时间戳: 2025-06-27 20:18:01.018097, 时间差: 0:01:36.191097
2025-06-27 20:18:01,524 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=1289，卖单数=1052
2025-06-27 20:18:01,525 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:01.525 [时间戳:1751026681525], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.37, 卖盘总量: 25.19, 点差: -0.0034%, 当前深度快照数量: 21
2025-06-27 20:18:02,038 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=1289，卖单数=1052
2025-06-27 20:18:02,038 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:02,039 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:02.039 [时间戳:1751026682039], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.37, 卖盘总量: 25.19, 点差: -0.0034%, 当前深度快照数量: 22
2025-06-27 20:18:02,043 - __main__ - INFO - 大量深度更新: btcusdt 有 63 个买单和 60 个卖单更新, ID从 7895651846182 更新到 7895651866581
2025-06-27 20:18:02,044 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:24.936000, 当前时间戳: 2025-06-27 20:18:02.044239, 时间差: 0:01:37.108239
2025-06-27 20:18:02,590 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=1293，卖单数=1059
2025-06-27 20:18:02,591 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:02.591 [时间戳:1751026682591], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.91, 卖盘总量: 29.30, 点差: -0.0034%, 当前深度快照数量: 23
2025-06-27 20:18:02,711 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:02,716 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.038000, 当前时间戳: 2025-06-27 20:18:02.716317, 时间差: 0:01:37.678317
2025-06-27 20:18:03,159 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=1291，卖单数=1060
2025-06-27 20:18:03,160 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:03.160 [时间戳:1751026683160], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.62, 卖盘总量: 29.18, 点差: -0.0034%, 当前深度快照数量: 24
2025-06-27 20:18:03,608 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:03,613 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.138000, 当前时间戳: 2025-06-27 20:18:03.613223, 时间差: 0:01:38.475223
2025-06-27 20:18:04,034 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=1297，卖单数=1058
2025-06-27 20:18:04,444 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:04.444 [时间戳:1751026684444], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.77, 卖盘总量: 29.46, 点差: -0.0034%, 当前深度快照数量: 25
2025-06-27 20:18:04,611 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=1297，卖单数=1058
2025-06-27 20:18:04,780 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:04.780 [时间戳:1751026684780], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.77, 卖盘总量: 29.46, 点差: -0.0034%, 当前深度快照数量: 26
2025-06-27 20:18:04,940 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:04,945 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.241000, 当前时间戳: 2025-06-27 20:18:04.945224, 时间差: 0:01:39.704224
2025-06-27 20:18:05,156 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=1297，卖单数=1074
2025-06-27 20:18:05,157 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:05.157 [时间戳:1751026685157], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.93, 卖盘总量: 28.60, 点差: -0.0034%, 当前深度快照数量: 27
2025-06-27 20:18:05,677 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=1297，卖单数=1074
2025-06-27 20:18:05,760 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:05.760 [时间戳:1751026685760], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 33.93, 卖盘总量: 28.60, 点差: -0.0034%, 当前深度快照数量: 28
2025-06-27 20:18:05,920 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:05,925 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.343000, 当前时间戳: 2025-06-27 20:18:05.925032, 时间差: 0:01:40.582032
2025-06-27 20:18:06,265 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=1297，卖单数=1076
2025-06-27 20:18:06,743 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:06.743 [时间戳:1751026686743], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.64, 卖盘总量: 28.60, 点差: -0.0034%, 当前深度快照数量: 29
2025-06-27 20:18:06,787 - __main__ - INFO - 数据库处理状态：已执行 0 次保存操作
2025-06-27 20:18:06,797 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=1297，卖单数=1076
2025-06-27 20:18:06,798 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:06.798 [时间戳:1751026686798], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.64, 卖盘总量: 28.60, 点差: -0.0034%, 当前深度快照数量: 30
2025-06-27 20:18:06,798 - __main__ - INFO - 快照调度状态：过去30秒新增了 51 个快照，当前总计 30 个快照
2025-06-27 20:18:06,908 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:06,912 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.187, 时间: 1751026585256
2025-06-27 20:18:07,347 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=1303，卖单数=1079
2025-06-27 20:18:07,348 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:07.348 [时间戳:1751026687348], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.53, 卖盘总量: 28.96, 点差: -0.0034%, 当前深度快照数量: 31
2025-06-27 20:18:07,815 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.445000, 当前时间戳: 2025-06-27 20:18:07.815522, 时间差: 0:01:42.370522
2025-06-27 20:18:07,893 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=1303，卖单数=1079
2025-06-27 20:18:07,977 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:07.977 [时间戳:1751026687977], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.53, 卖盘总量: 28.96, 点差: -0.0034%, 当前深度快照数量: 32
2025-06-27 20:18:08,472 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=1303，卖单数=1079
2025-06-27 20:18:08,472 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:08,473 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:08.473 [时间戳:1751026688473], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.53, 卖盘总量: 28.96, 点差: -0.0034%, 当前深度快照数量: 33
2025-06-27 20:18:08,477 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.543000, 当前时间戳: 2025-06-27 20:18:08.477614, 时间差: 0:01:42.934614
2025-06-27 20:18:08,989 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=1306，卖单数=1081
2025-06-27 20:18:08,990 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:08.990 [时间戳:1751026688990], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.58, 卖盘总量: 29.46, 点差: -0.0034%, 当前深度快照数量: 34
2025-06-27 20:18:09,158 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:09,164 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.653000, 当前时间戳: 2025-06-27 20:18:09.164160, 时间差: 0:01:43.511160
2025-06-27 20:18:09,494 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=1309，卖单数=1090
2025-06-27 20:18:09,901 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 34
2025-06-27 20:18:09,987 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:09.987 [时间戳:1751026689987], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.66, 卖盘总量: 28.64, 点差: -0.0034%, 当前深度快照数量: 35
2025-06-27 20:18:09,988 - __main__ - INFO - 准备保存 35 条快照数据，并已清空全局快照列表
2025-06-27 20:18:10,059 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=1309，卖单数=1090
2025-06-27 20:18:10,135 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:10.135 [时间戳:1751026690135], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.66, 卖盘总量: 28.64, 点差: -0.0034%, 当前深度快照数量: 1
2025-06-27 20:18:10,465 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:10,470 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.757000, 当前时间戳: 2025-06-27 20:18:10.470320, 时间差: 0:01:44.713320
2025-06-27 20:18:10,638 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=1311，卖单数=1091
2025-06-27 20:18:10,639 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:10.638 [时间戳:1751026690638], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.81, 卖盘总量: 28.41, 点差: -0.0034%, 当前深度快照数量: 2
2025-06-27 20:18:10,713 - __main__ - INFO - 成功保存 35 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:18:11,285 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=1311，卖单数=1091
2025-06-27 20:18:11,522 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:11.522 [时间戳:1751026691522], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.81, 卖盘总量: 28.41, 点差: -0.0034%, 当前深度快照数量: 3
2025-06-27 20:18:11,522 - __main__ - INFO - 共保存了 35 条深度快照数据到MariaDB
2025-06-27 20:18:11,792 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=1311，卖单数=1091
2025-06-27 20:18:11,795 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:11.794 [时间戳:1751026691794], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.81, 卖盘总量: 28.41, 点差: -0.0034%, 当前深度快照数量: 4
2025-06-27 20:18:11,931 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:11,939 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.858000, 当前时间戳: 2025-06-27 20:18:11.939840, 时间差: 0:01:46.081840
2025-06-27 20:18:12,310 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=1312，卖单数=1094
2025-06-27 20:18:12,311 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:12.311 [时间戳:1751026692311], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.55, 卖盘总量: 28.51, 点差: -0.0034%, 当前深度快照数量: 5
2025-06-27 20:18:12,811 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=1312，卖单数=1094
2025-06-27 20:18:12,812 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:12.812 [时间戳:1751026692812], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.55, 卖盘总量: 28.51, 点差: -0.0034%, 当前深度快照数量: 6
2025-06-27 20:18:12,925 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:12,930 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:25.958000, 当前时间戳: 2025-06-27 20:18:12.930137, 时间差: 0:01:46.972137
2025-06-27 20:18:13,329 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=1315，卖单数=1097
2025-06-27 20:18:13,414 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:13.414 [时间戳:1751026693414], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.89, 卖盘总量: 28.04, 点差: -0.0034%, 当前深度快照数量: 7
2025-06-27 20:18:13,582 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:13,587 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.061000, 当前时间戳: 2025-06-27 20:18:13.587683, 时间差: 0:01:47.526683
2025-06-27 20:18:13,964 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=1314，卖单数=1111
2025-06-27 20:18:14,036 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:14.036 [时间戳:1751026694036], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.60, 卖盘总量: 28.36, 点差: -0.0034%, 当前深度快照数量: 8
2025-06-27 20:18:14,579 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=1314，卖单数=1111
2025-06-27 20:18:14,654 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:14.654 [时间戳:1751026694654], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.60, 卖盘总量: 28.36, 点差: -0.0034%, 当前深度快照数量: 9
2025-06-27 20:18:15,097 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=1314，卖单数=1111
2025-06-27 20:18:15,097 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:15,098 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:15.098 [时间戳:1751026695098], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.60, 卖盘总量: 28.36, 点差: -0.0034%, 当前深度快照数量: 10
2025-06-27 20:18:15,103 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.001, 时间: 1751026585934
2025-06-27 20:18:13,986 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.164000, 当前时间戳: 2025-06-27 20:18:13.986005, 时间差: 0:01:47.822005
2025-06-27 20:18:14,646 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:14,650 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.264000, 当前时间戳: 2025-06-27 20:18:14.650592, 时间差: 0:01:48.386592
2025-06-27 20:18:15,629 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=1318，卖单数=1121
2025-06-27 20:18:15,630 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:15,631 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:15.631 [时间戳:1751026695631], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.02, 卖盘总量: 28.27, 点差: -0.0034%, 当前深度快照数量: 11
2025-06-27 20:18:15,634 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.016, 时间: 1751026586136
2025-06-27 20:18:16,135 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=1317，卖单数=1118
2025-06-27 20:18:16,136 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:16.136 [时间戳:1751026696136], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.93, 卖盘总量: 28.36, 点差: -0.0034%, 当前深度快照数量: 12
2025-06-27 20:18:16,513 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.369000, 当前时间戳: 2025-06-27 20:18:16.513566, 时间差: 0:01:50.144566
2025-06-27 20:18:16,678 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=1317，卖单数=1118
2025-06-27 20:18:16,679 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:16.679 [时间戳:1751026696679], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.93, 卖盘总量: 28.36, 点差: -0.0034%, 当前深度快照数量: 13
2025-06-27 20:18:17,154 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:17,158 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.469000, 当前时间戳: 2025-06-27 20:18:17.158522, 时间差: 0:01:50.689522
2025-06-27 20:18:17,240 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=1314，卖单数=1121
2025-06-27 20:18:17,318 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:17.318 [时间戳:1751026697318], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.72, 卖盘总量: 28.95, 点差: -0.0034%, 当前深度快照数量: 14
2025-06-27 20:18:17,829 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:17,829 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=1314，卖单数=1121
2025-06-27 20:18:17,834 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.20, 数量: 0.002, 时间: 1751026586395
2025-06-27 20:18:17,834 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:17.834 [时间戳:1751026697834], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.72, 卖盘总量: 28.95, 点差: -0.0034%, 当前深度快照数量: 15
2025-06-27 20:18:18,378 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=1316，卖单数=1123
2025-06-27 20:18:18,379 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:18.379 [时间戳:1751026698379], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.72, 卖盘总量: 28.95, 点差: -0.0034%, 当前深度快照数量: 16
2025-06-27 20:18:18,487 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.571000, 当前时间戳: 2025-06-27 20:18:18.487101, 时间差: 0:01:51.916101
2025-06-27 20:18:18,925 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=1316，卖单数=1123
2025-06-27 20:18:18,926 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:18.926 [时间戳:1751026698926], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.72, 卖盘总量: 28.95, 点差: -0.0034%, 当前深度快照数量: 17
2025-06-27 20:18:19,067 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:19,072 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.666000, 当前时间戳: 2025-06-27 20:18:19.072660, 时间差: 0:01:52.406660
2025-06-27 20:18:19,777 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=1318，卖单数=1125
2025-06-27 20:18:19,855 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:19.855 [时间戳:1751026699855], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 34.80, 卖盘总量: 29.02, 点差: -0.0034%, 当前深度快照数量: 18
2025-06-27 20:18:20,016 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:20,021 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.001, 时间: 1751026586522
2025-06-27 20:18:20,356 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=1318，卖单数=1133
2025-06-27 20:18:20,837 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:20.837 [时间戳:1751026700837], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.17, 卖盘总量: 28.11, 点差: -0.0034%, 当前深度快照数量: 19
2025-06-27 20:18:20,888 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=1318，卖单数=1133
2025-06-27 20:18:20,889 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:20.889 [时间戳:1751026700889], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.17, 卖盘总量: 28.11, 点差: -0.0034%, 当前深度快照数量: 20
2025-06-27 20:18:21,000 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.771000, 当前时间戳: 2025-06-27 20:18:20.999990, 时间差: 0:01:54.228990
2025-06-27 20:18:21,410 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=1318，卖单数=1133
2025-06-27 20:18:21,795 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:21.795 [时间戳:1751026701795], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.17, 卖盘总量: 28.11, 点差: -0.0034%, 当前深度快照数量: 21
2025-06-27 20:18:21,961 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=1318，卖单数=1133
2025-06-27 20:18:21,961 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:21,962 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:21.962 [时间戳:1751026701962], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.17, 卖盘总量: 28.11, 点差: -0.0034%, 当前深度快照数量: 22
2025-06-27 20:18:21,966 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.874000, 当前时间戳: 2025-06-27 20:18:21.966418, 时间差: 0:01:55.092418
2025-06-27 20:18:22,485 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=1322，卖单数=1131
2025-06-27 20:18:22,486 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:22.486 [时间戳:1751026702486], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.26, 卖盘总量: 28.38, 点差: -0.0034%, 当前深度快照数量: 23
2025-06-27 20:18:22,867 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:22,871 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106922.30, 数量: 0.296, 时间: 1751026586734
2025-06-27 20:18:23,032 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=1322，卖单数=1134
2025-06-27 20:18:23,034 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:23.033 [时间戳:1751026703034], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.17, 卖盘总量: 28.31, 点差: -0.0034%, 当前深度快照数量: 24
2025-06-27 20:18:23,603 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=1322，卖单数=1134
2025-06-27 20:18:23,686 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:23.686 [时间戳:1751026703686], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.17, 卖盘总量: 28.31, 点差: -0.0034%, 当前深度快照数量: 25
2025-06-27 20:18:24,190 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=1322，卖单数=1134
2025-06-27 20:18:24,191 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:26.979000, 当前时间戳: 2025-06-27 20:18:24.191006, 时间差: 0:01:57.212006
2025-06-27 20:18:24,191 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:24.191 [时间戳:1751026704191], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.17, 卖盘总量: 28.31, 点差: -0.0034%, 当前深度快照数量: 26
2025-06-27 20:18:24,694 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=1322，卖单数=1134
2025-06-27 20:18:24,695 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:24.695 [时间戳:1751026704695], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.17, 卖盘总量: 28.31, 点差: -0.0034%, 当前深度快照数量: 27
2025-06-27 20:18:24,857 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:24,862 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:27.078000, 当前时间戳: 2025-06-27 20:18:24.862014, 时间差: 0:01:57.784014
2025-06-27 20:18:25,277 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=1323，卖单数=1134
2025-06-27 20:18:25,686 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:25.686 [时间戳:1751026705686], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.01, 卖盘总量: 28.72, 点差: -0.0034%, 当前深度快照数量: 28
2025-06-27 20:18:25,848 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=1323，卖单数=1134
2025-06-27 20:18:25,848 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:25,849 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:25.849 [时间戳:1751026705849], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.01, 卖盘总量: 28.72, 点差: -0.0034%, 当前深度快照数量: 29
2025-06-27 20:18:25,853 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:27.181000, 当前时间戳: 2025-06-27 20:18:25.853470, 时间差: 0:01:58.672470
2025-06-27 20:18:26,733 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=1321，卖单数=1133
2025-06-27 20:18:26,733 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:26,734 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:26.734 [时间戳:1751026706734], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.01, 卖盘总量: 28.49, 点差: -0.0034%, 当前深度快照数量: 30
2025-06-27 20:18:26,738 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:27.281000, 当前时间戳: 2025-06-27 20:18:26.738326, 时间差: 0:01:59.457326
2025-06-27 20:18:27,278 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=1323，卖单数=1135
2025-06-27 20:18:27,279 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:27.279 [时间戳:1751026707279], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 35.04, 卖盘总量: 28.48, 点差: -0.0034%, 当前深度快照数量: 31
2025-06-27 20:18:27,384 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:27,389 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:16:27.381000, 当前时间戳: 2025-06-27 20:18:27.389261, 时间差: 0:02:00.008261
2025-06-27 20:18:27,811 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=1324，卖单数=1133
2025-06-27 20:18:28,214 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:28.214 [时间戳:1751026708214], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.71, 卖盘总量: 27.91, 点差: -0.0034%, 当前深度快照数量: 32
2025-06-27 20:18:28,382 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=1324，卖单数=1133
2025-06-27 20:18:28,382 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:28,383 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:28.383 [时间戳:1751026708383], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.71, 卖盘总量: 27.91, 点差: -0.0034%, 当前深度快照数量: 33
2025-06-27 20:18:28,387 - __main__ - ERROR - WebSocket连接错误: ping/pong timed out
2025-06-27 20:18:28,388 - websocket - ERROR - ping/pong timed out - goodbye
2025-06-27 20:18:28,571 - __main__ - WARNING - WebSocket连接关闭: None None
2025-06-27 20:18:28,571 - __main__ - INFO - 触发WebSocket重连...
2025-06-27 20:18:28,572 - __main__ - INFO - 等待重连事件...
2025-06-27 20:18:28,572 - __main__ - INFO - 收到重连事件，准备重新连接...
2025-06-27 20:18:28,572 - __main__ - INFO - 正在创建WebSocket连接，准备订阅 BTCUSDT
2025-06-27 20:18:28,869 - websocket - INFO - Websocket connected
2025-06-27 20:18:28,875 - __main__ - INFO - 已连接到Binance WebSocket，准备订阅 BTCUSDT 数据
2025-06-27 20:18:28,879 - __main__ - INFO - 已发送 BTCUSDT 订阅请求: 深度更新和聚合交易
2025-06-27 20:18:28,880 - __main__ - INFO - 尝试初始化订单簿 (第 1/5 次)
2025-06-27 20:18:28,887 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:18:28,887 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:18:28,891 - __main__ - WARNING - 订单簿初始化失败，2秒后重试...
2025-06-27 20:18:28,914 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=1328，卖单数=1132
2025-06-27 20:18:28,915 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:28.915 [时间戳:1751026708915], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.34, 卖盘总量: 28.48, 点差: -0.0034%, 当前深度快照数量: 34
2025-06-27 20:18:29,166 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.012000, 当前时间戳: 2025-06-27 20:18:29.166113, 时间差: 0:00:01.154113
2025-06-27 20:18:29,496 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 34，订单簿状态: 初始化=False，买单数=1328，卖单数=1132
2025-06-27 20:18:29,888 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:29.888 [时间戳:1751026709888], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.34, 卖盘总量: 28.48, 点差: -0.0034%, 当前深度快照数量: 35
2025-06-27 20:18:30,025 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 35，订单簿状态: 初始化=False，买单数=1328，卖单数=1132
2025-06-27 20:18:30,026 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:30.026 [时间戳:1751026710026], 买盘最高价: 106922.2, 卖盘最低价: 106918.6, 买盘总量: 36.34, 卖盘总量: 28.48, 点差: -0.0034%, 当前深度快照数量: 36
2025-06-27 20:18:30,134 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:30,138 - __main__ - INFO - 大量深度更新: btcusdt 有 52 个买单和 29 个卖单更新, ID从 7895652046592 更新到 7895661859639
2025-06-27 20:18:30,139 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.110000, 当前时间戳: 2025-06-27 20:18:30.139271, 时间差: 0:00:02.029271
2025-06-27 20:18:30,802 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 36，订单簿状态: 初始化=False，买单数=1351，卖单数=1148
2025-06-27 20:18:30,874 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:30.874 [时间戳:1751026710874], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 49.73, 点差: -0.0156%, 当前深度快照数量: 37
2025-06-27 20:18:30,938 - __main__ - INFO - 尝试初始化订单簿 (第 2/5 次)
2025-06-27 20:18:30,938 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:18:30,939 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:18:30,942 - __main__ - WARNING - 订单簿初始化失败，4秒后重试...
2025-06-27 20:18:31,017 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:31,022 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.218000, 当前时间戳: 2025-06-27 20:18:31.022357, 时间差: 0:00:02.804357
2025-06-27 20:18:31,334 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 37，订单簿状态: 初始化=False，买单数=1360，卖单数=1156
2025-06-27 20:18:31,493 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:31.493 [时间戳:1751026711493], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 48.41, 点差: -0.0156%, 当前深度快照数量: 38
2025-06-27 20:18:31,868 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 38，订单簿状态: 初始化=False，买单数=1360，卖单数=1156
2025-06-27 20:18:31,869 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:31.869 [时间戳:1751026711869], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 48.41, 点差: -0.0156%, 当前深度快照数量: 39
2025-06-27 20:18:32,265 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:32,270 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106905.50, 数量: 0.004, 时间: 1751026708150
2025-06-27 20:18:32,434 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 39
2025-06-27 20:18:32,434 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 39，订单簿状态: 初始化=False，买单数=1362，卖单数=1168
2025-06-27 20:18:32,435 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:32.435 [时间戳:1751026712435], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 55.12, 点差: -0.0156%, 当前深度快照数量: 40
2025-06-27 20:18:32,435 - __main__ - INFO - 准备保存 40 条快照数据，并已清空全局快照列表
2025-06-27 20:18:33,038 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=1362，卖单数=1168
2025-06-27 20:18:33,039 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.319000, 当前时间戳: 2025-06-27 20:18:33.039330, 时间差: 0:00:04.720330
2025-06-27 20:18:33,039 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:33.039 [时间戳:1751026713039], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 55.12, 点差: -0.0156%, 当前深度快照数量: 1
2025-06-27 20:18:33,619 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=1362，卖单数=1168
2025-06-27 20:18:33,619 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:33,620 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:33.620 [时间戳:1751026713620], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 55.12, 点差: -0.0156%, 当前深度快照数量: 2
2025-06-27 20:18:33,624 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.421000, 当前时间戳: 2025-06-27 20:18:33.624252, 时间差: 0:00:05.203252
2025-06-27 20:18:34,200 - __main__ - INFO - 成功保存 40 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:18:34,200 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=1370，卖单数=1179
2025-06-27 20:18:34,373 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:34.373 [时间戳:1751026714373], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 56.50, 点差: -0.0156%, 当前深度快照数量: 3
2025-06-27 20:18:34,791 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=1370，卖单数=1179
2025-06-27 20:18:34,791 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:34,792 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:34.792 [时间戳:1751026714792], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 56.50, 点差: -0.0156%, 当前深度快照数量: 4
2025-06-27 20:18:34,796 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106905.40, 数量: 0.004, 时间: 1751026708417
2025-06-27 20:18:35,044 - __main__ - INFO - 尝试初始化订单簿 (第 3/5 次)
2025-06-27 20:18:35,044 - __main__ - INFO - 共保存了 40 条深度快照数据到MariaDB
2025-06-27 20:18:35,044 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:18:35,045 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:18:35,048 - __main__ - WARNING - 订单簿初始化失败，8秒后重试...
2025-06-27 20:18:35,301 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=1376，卖单数=1187
2025-06-27 20:18:35,387 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:35.387 [时间戳:1751026715387], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 46.27, 点差: -0.0156%, 当前深度快照数量: 5
2025-06-27 20:18:35,871 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=1376，卖单数=1187
2025-06-27 20:18:35,872 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.521000, 当前时间戳: 2025-06-27 20:18:35.872257, 时间差: 0:00:07.351257
2025-06-27 20:18:35,872 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:35.872 [时间戳:1751026715872], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 46.27, 点差: -0.0156%, 当前深度快照数量: 6
2025-06-27 20:18:36,558 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=1376，卖单数=1187
2025-06-27 20:18:36,630 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:36.630 [时间戳:1751026716630], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 46.27, 点差: -0.0156%, 当前深度快照数量: 7
2025-06-27 20:18:36,774 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:36,779 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.621000, 当前时间戳: 2025-06-27 20:18:36.779526, 时间差: 0:00:08.158526
2025-06-27 20:18:36,862 - __main__ - INFO - 快照调度状态：过去30秒新增了 52 个快照，当前总计 7 个快照
2025-06-27 20:18:37,109 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=1378，卖单数=1192
2025-06-27 20:18:37,278 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:37.278 [时间戳:1751026717278], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 45.83, 点差: -0.0156%, 当前深度快照数量: 8
2025-06-27 20:18:37,437 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:37,441 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106905.50, 数量: 0.192, 时间: 1751026708480
2025-06-27 20:18:37,831 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=1385，卖单数=1211
2025-06-27 20:18:37,915 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:37.915 [时间戳:1751026717915], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.87, 点差: -0.0156%, 当前深度快照数量: 9
2025-06-27 20:18:38,554 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=1385，卖单数=1211
2025-06-27 20:18:38,555 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:38.555 [时间戳:1751026718555], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.87, 点差: -0.0156%, 当前深度快照数量: 10
2025-06-27 20:18:38,717 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.723000, 当前时间戳: 2025-06-27 20:18:38.717879, 时间差: 0:00:09.994879
2025-06-27 20:18:39,130 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=1385，卖单数=1211
2025-06-27 20:18:39,216 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:39.216 [时间戳:1751026719216], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.87, 点差: -0.0156%, 当前深度快照数量: 11
2025-06-27 20:18:39,381 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:39,385 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.825000, 当前时间戳: 2025-06-27 20:18:39.385864, 时间差: 0:00:10.560864
2025-06-27 20:18:39,776 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=1384，卖单数=1210
2025-06-27 20:18:39,848 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:39.848 [时间戳:1751026719848], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.81, 点差: -0.0156%, 当前深度快照数量: 12
2025-06-27 20:18:40,279 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=1384，卖单数=1210
2025-06-27 20:18:40,279 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:40,280 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:40.280 [时间戳:1751026720280], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.81, 点差: -0.0156%, 当前深度快照数量: 13
2025-06-27 20:18:40,284 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:28.927000, 当前时间戳: 2025-06-27 20:18:40.284741, 时间差: 0:00:11.357741
2025-06-27 20:18:41,092 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=1385，卖单数=1220
2025-06-27 20:18:41,092 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:41.092 [时间戳:1751026721092], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.82, 点差: -0.0156%, 当前深度快照数量: 14
2025-06-27 20:18:41,584 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:41,589 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.030000, 当前时间戳: 2025-06-27 20:18:41.589522, 时间差: 0:00:12.559522
2025-06-27 20:18:41,676 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=1390，卖单数=1228
2025-06-27 20:18:42,092 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:42.092 [时间戳:1751026722092], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.62, 点差: -0.0156%, 当前深度快照数量: 15
2025-06-27 20:18:42,264 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=1390，卖单数=1228
2025-06-27 20:18:42,426 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:42.426 [时间戳:1751026722426], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.62, 点差: -0.0156%, 当前深度快照数量: 16
2025-06-27 20:18:42,602 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:42,608 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.128000, 当前时间戳: 2025-06-27 20:18:42.608755, 时间差: 0:00:13.480755
2025-06-27 20:18:42,767 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=1394，卖单数=1228
2025-06-27 20:18:42,767 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:42.767 [时间戳:1751026722767], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.99, 点差: -0.0156%, 当前深度快照数量: 17
2025-06-27 20:18:43,104 - __main__ - INFO - 尝试初始化订单簿 (第 4/5 次)
2025-06-27 20:18:43,105 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:18:43,105 - __main__ - ERROR - 初始化订单簿失败: name 'get_latest_context_state' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 1080, in initialize_orderbook
    state_data = get_latest_context_state(current_symbol)
                 ^^^^^^^^^^^^^^^^^^^^^^^^
NameError: name 'get_latest_context_state' is not defined
2025-06-27 20:18:43,109 - __main__ - WARNING - 订单簿初始化失败，16秒后重试...
2025-06-27 20:18:43,271 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=1394，卖单数=1228
2025-06-27 20:18:43,272 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:43,273 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:43.273 [时间戳:1751026723273], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.99, 点差: -0.0156%, 当前深度快照数量: 18
2025-06-27 20:18:43,276 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.231000, 当前时间戳: 2025-06-27 20:18:43.276829, 时间差: 0:00:14.045829
2025-06-27 20:18:44,022 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=1396，卖单数=1231
2025-06-27 20:18:44,023 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:44.023 [时间戳:1751026724023], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 43.98, 点差: -0.0156%, 当前深度快照数量: 19
2025-06-27 20:18:44,468 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:44,472 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.336000, 当前时间戳: 2025-06-27 20:18:44.472325, 时间差: 0:00:15.136325
2025-06-27 20:18:44,550 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=1397，卖单数=1237
2025-06-27 20:18:44,633 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:44.633 [时间戳:1751026724633], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 43.98, 点差: -0.0156%, 当前深度快照数量: 20
2025-06-27 20:18:45,438 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=1397，卖单数=1237
2025-06-27 20:18:45,438 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:45,439 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:45.439 [时间戳:1751026725439], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 43.98, 点差: -0.0156%, 当前深度快照数量: 21
2025-06-27 20:18:45,443 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.438000, 当前时间戳: 2025-06-27 20:18:45.443451, 时间差: 0:00:16.005451
2025-06-27 20:18:46,286 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=1398，卖单数=1242
2025-06-27 20:18:46,287 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:46.287 [时间戳:1751026726287], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.97, 点差: -0.0156%, 当前深度快照数量: 22
2025-06-27 20:18:46,875 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=1398，卖单数=1242
2025-06-27 20:18:46,959 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:46.958 [时间戳:1751026726958], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.97, 点差: -0.0156%, 当前深度快照数量: 23
2025-06-27 20:18:47,128 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:47,133 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.538000, 当前时间戳: 2025-06-27 20:18:47.133338, 时间差: 0:00:17.595338
2025-06-27 20:18:47,464 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=1404，卖单数=1243
2025-06-27 20:18:47,961 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:47.961 [时间戳:1751026727961], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.96, 点差: -0.0156%, 当前深度快照数量: 24
2025-06-27 20:18:48,014 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=1404，卖单数=1243
2025-06-27 20:18:48,015 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:48.015 [时间戳:1751026728015], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.96, 点差: -0.0156%, 当前深度快照数量: 25
2025-06-27 20:18:48,451 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:48,455 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.641000, 当前时间戳: 2025-06-27 20:18:48.455643, 时间差: 0:00:18.814643
2025-06-27 20:18:48,527 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=1404，卖单数=1247
2025-06-27 20:18:48,599 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:48.599 [时间戳:1751026728599], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 45.01, 点差: -0.0156%, 当前深度快照数量: 26
2025-06-27 20:18:49,182 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=1404，卖单数=1247
2025-06-27 20:18:49,183 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:49.183 [时间戳:1751026729183], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 45.01, 点差: -0.0156%, 当前深度快照数量: 27
2025-06-27 20:18:49,325 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: False, 缓存的深度更新: 0, 快照数量: 27
2025-06-27 20:18:49,325 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:49,330 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.741000, 当前时间戳: 2025-06-27 20:18:49.330372, 时间差: 0:00:19.589372
2025-06-27 20:18:49,699 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 27，订单簿状态: 初始化=False，买单数=1406，卖单数=1252
2025-06-27 20:18:49,700 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:49.700 [时间戳:1751026729700], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.83, 点差: -0.0156%, 当前深度快照数量: 28
2025-06-27 20:18:50,299 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 28，订单簿状态: 初始化=False，买单数=1406，卖单数=1252
2025-06-27 20:18:50,299 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:50,300 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:50.300 [时间戳:1751026730300], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 44.83, 点差: -0.0156%, 当前深度快照数量: 29
2025-06-27 20:18:50,304 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.841000, 当前时间戳: 2025-06-27 20:18:50.304304, 时间差: 0:00:20.463304
2025-06-27 20:18:50,852 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 29，订单簿状态: 初始化=False，买单数=1406，卖单数=1255
2025-06-27 20:18:50,853 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:50.853 [时间戳:1751026730853], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 45.40, 点差: -0.0156%, 当前深度快照数量: 30
2025-06-27 20:18:50,961 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:50,966 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106905.40, 数量: 0.005, 时间: 1751026709740
2025-06-27 20:18:51,390 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 30，订单簿状态: 初始化=False，买单数=1412，卖单数=1267
2025-06-27 20:18:51,470 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:51.470 [时间戳:1751026731470], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 46.53, 点差: -0.0156%, 当前深度快照数量: 31
2025-06-27 20:18:51,633 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:29.945000, 当前时间戳: 2025-06-27 20:18:51.633127, 时间差: 0:00:21.688127
2025-06-27 20:18:51,922 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 31，订单簿状态: 初始化=False，买单数=1412，卖单数=1267
2025-06-27 20:18:52,065 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:52.065 [时间戳:1751026732065], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 46.53, 点差: -0.0156%, 当前深度快照数量: 32
2025-06-27 20:18:52,209 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:52,214 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:30.041000, 当前时间戳: 2025-06-27 20:18:52.214867, 时间差: 0:00:22.173867
2025-06-27 20:18:52,435 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 32，订单簿状态: 初始化=False，买单数=1410，卖单数=1274
2025-06-27 20:18:52,436 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:52.436 [时间戳:1751026732436], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 46.85, 点差: -0.0156%, 当前深度快照数量: 33
2025-06-27 20:18:52,866 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:52,872 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:18:30.145000, 当前时间戳: 2025-06-27 20:18:52.872779, 时间差: 0:00:22.727779
2025-06-27 20:18:52,955 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 33，订单簿状态: 初始化=False，买单数=1406，卖单数=1277
2025-06-27 20:18:53,038 - __main__ - INFO - 深度快照已生成: solusdt @ 20:18:53.038 [时间戳:1751026733038], 买盘最高价: 106922.2, 卖盘最低价: 106905.5, 买盘总量: 36.34, 卖盘总量: 46.85, 点差: -0.0156%, 当前深度快照数量: 34
2025-06-27 20:18:53,210 - __main__ - INFO - 收到信号 2，准备关闭程序...
2025-06-27 20:18:53,534 - __main__ - ERROR - 保存深度更新数据失败: name 'save_to_context_table' is not defined
Traceback (most recent call last):
  File "/mnt/d/python_quant/python量化/order_books/update/update_book_1.py", line 505, in save_depth_update
    save_to_context_table(update, 'update')
    ^^^^^^^^^^^^^^^^^^^^^
NameError: name 'save_to_context_table' is not defined
2025-06-27 20:18:53,538 - __main__ - WARNING - WebSocket连接关闭: None None
2025-06-27 20:18:53,539 - __main__ - INFO - 触发WebSocket重连...
2025-06-27 20:18:53,539 - __main__ - INFO - WebSocket线程结束
2025-06-27 20:18:53,576 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 34
2025-06-27 20:18:53,577 - __main__ - INFO - 准备保存 34 条快照数据，并已清空全局快照列表
2025-06-27 20:18:54,297 - __main__ - INFO - 数据库处理线程已结束
2025-06-27 20:18:54,410 - __main__ - INFO - 成功保存 34 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 20:18:54,736 - __main__ - INFO - 共保存了 34 条深度快照数据到MariaDB
2025-06-27 20:18:54,737 - __main__ - INFO - 程序已关闭
2025-06-27 20:42:43,053 - __main__ - INFO - MariaDB连接池创建成功
2025-06-27 20:42:43,054 - __main__ - INFO - 开始执行订单簿更新程序
2025-06-27 20:42:43,142 - __main__ - INFO - 已成功创建数据库连接
2025-06-27 20:42:44,345 - __main__ - INFO - MariaDB数据库和表已初始化
2025-06-27 20:42:44,655 - __main__ - INFO - 准备获取最具波动性的交易对
2025-06-27 20:42:45,656 - __main__ - INFO - 正在获取币安期货交易对列表 (第 1/3 次尝试)...
2025-06-27 20:42:47,102 - __main__ - INFO - 获取到 476 个交易中的期货交易对
2025-06-27 20:42:47,103 - __main__ - INFO - 成功获取交易对列表，等待2秒后获取24小时统计...
2025-06-27 20:42:49,123 - __main__ - INFO - 正在获取24小时价格统计 (第 1/3 次尝试)...
2025-06-27 20:42:51,154 - __main__ - INFO - 成功获取到 521 个交易对的24小时统计
2025-06-27 20:42:51,156 - __main__ - INFO - 已获取并排序 475 个交易对的24小时统计
2025-06-27 20:42:51,156 - __main__ - INFO - 排名1: SAHARAUSDT - 波动率: 33.83%, 成交量: 2274608832.63 USDT, 波动率得分: 1613306.35
2025-06-27 20:42:51,156 - __main__ - INFO - 排名2: BANANAS31USDT - 波动率: 31.54%, 成交量: 848355705.47 USDT, 波动率得分: 918563.89
2025-06-27 20:42:51,156 - __main__ - INFO - 排名3: HIFIUSDT - 波动率: 39.66%, 成交量: 201975651.53 USDT, 波动率得分: 563626.32
2025-06-27 20:42:51,156 - __main__ - INFO - 排名4: BSWUSDT - 波动率: 33.97%, 成交量: 266672334.40 USDT, 波动率得分: 554668.35
2025-06-27 20:42:51,156 - __main__ - INFO - 排名5: SEIUSDT - 波动率: 11.97%, 成交量: 560781672.83 USDT, 波动率得分: 283530.57
2025-06-27 20:42:51,157 - __main__ - INFO - 排名6: HUSDT - 波动率: 10.67%, 成交量: 251048997.53 USDT, 波动率得分: 169061.09
2025-06-27 20:42:51,157 - __main__ - INFO - 排名7: XRPUSDT - 波动率: 3.60%, 成交量: 1400033636.08 USDT, 波动率得分: 134888.37
2025-06-27 20:42:51,157 - __main__ - INFO - 排名8: 1000000BOBUSDT - 波动率: 22.38%, 成交量: 22768142.39 USDT, 波动率得分: 106764.49
2025-06-27 20:42:51,157 - __main__ - INFO - 排名9: APTUSDT - 波动率: 5.49%, 成交量: 305024647.71 USDT, 波动率得分: 95847.67
2025-06-27 20:42:51,157 - __main__ - INFO - 排名10: LQTYUSDT - 波动率: 8.79%, 成交量: 73176633.02 USDT, 波动率得分: 75166.93
2025-06-27 20:42:51,158 - __main__ - INFO - 正在获取币安期货交易对列表 (第 1/3 次尝试)...
2025-06-27 20:42:52,861 - __main__ - INFO - 获取到 476 个交易中的期货交易对
2025-06-27 20:42:52,863 - __main__ - INFO - 正在获取24小时价格统计 (第 1/3 次尝试)...
2025-06-27 20:42:54,470 - __main__ - INFO - 成功获取到 521 个交易对的24小时统计
2025-06-27 20:42:54,472 - __main__ - INFO - 已获取并排序 475 个交易对的24小时统计
2025-06-27 20:42:54,473 - __main__ - INFO - 排名1: SAHARAUSDT - 波动率: 33.83%, 成交量: 2274701578.36 USDT, 波动率得分: 1613339.24
2025-06-27 20:42:54,473 - __main__ - INFO - 排名2: BANANAS31USDT - 波动率: 31.59%, 成交量: 848402572.53 USDT, 波动率得分: 920249.53
2025-06-27 20:42:54,473 - __main__ - INFO - 排名3: HIFIUSDT - 波动率: 39.76%, 成交量: 201987034.46 USDT, 波动率得分: 565063.43
2025-06-27 20:42:54,473 - __main__ - INFO - 排名4: BSWUSDT - 波动率: 33.97%, 成交量: 266675564.99 USDT, 波动率得分: 554671.71
2025-06-27 20:42:54,473 - __main__ - INFO - 排名5: SEIUSDT - 波动率: 11.94%, 成交量: 560858462.75 USDT, 波动率得分: 282650.05
2025-06-27 20:42:54,474 - __main__ - INFO - 排名6: HUSDT - 波动率: 10.63%, 成交量: 251052145.64 USDT, 波动率得分: 168364.99
2025-06-27 20:42:54,474 - __main__ - INFO - 排名7: XRPUSDT - 波动率: 3.61%, 成交量: 1400055573.24 USDT, 波动率得分: 135076.51
2025-06-27 20:42:54,474 - __main__ - INFO - 排名8: 1000000BOBUSDT - 波动率: 22.27%, 成交量: 22777418.69 USDT, 波动率得分: 106294.67
2025-06-27 20:42:54,474 - __main__ - INFO - 排名9: APTUSDT - 波动率: 5.45%, 成交量: 305035288.93 USDT, 波动率得分: 95255.53
2025-06-27 20:42:54,474 - __main__ - INFO - 排名10: LQTYUSDT - 波动率: 8.71%, 成交量: 73177951.37 USDT, 波动率得分: 74534.58
2025-06-27 20:42:54,475 - __main__ - INFO - 当前波动最大的交易对为: SAHARAUSDT, 24小时价格变化: 33.83%, 成交量: 2274701578.36 USDT
2025-06-27 20:42:54,476 - __main__ - INFO - 选择波动性最大的交易对: BTCUSDT
2025-06-27 20:42:54,476 - __main__ - INFO - 初始交易对设置为: BTCUSDT
2025-06-27 20:42:54,476 - __main__ - INFO - 正在创建WebSocket连接，准备订阅 BTCUSDT
2025-06-27 20:42:54,476 - __main__ - INFO - 数据库处理线程已启动
2025-06-27 20:42:54,477 - __main__ - INFO - 开始调度快照生成任务，每500毫秒生成一次订单簿快照
2025-06-27 20:42:54,477 - __main__ - INFO - 数据库线程初始化时尝试立即保存一次
2025-06-27 20:42:54,477 - __main__ - INFO - 快照生成线程已启动
2025-06-27 20:42:54,477 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:42:54,478 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 0
2025-06-27 20:42:54,478 - __main__ - INFO - 等待WebSocket连接建立... (尝试 1/5)
2025-06-27 20:42:54,478 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:42:54,479 - __main__ - WARNING - 没有深度快照数据需要保存 - 检查take_snapshot函数是否正常运行
2025-06-27 20:42:54,898 - websocket - INFO - Websocket connected
2025-06-27 20:42:54,898 - __main__ - INFO - 已连接到Binance WebSocket，准备订阅 BTCUSDT 数据
2025-06-27 20:42:54,899 - __main__ - INFO - 已发送 BTCUSDT 订阅请求: 深度更新和聚合交易
2025-06-27 20:42:54,899 - __main__ - INFO - 尝试初始化订单簿 (第 1/5 次)
2025-06-27 20:42:54,899 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 20:42:55,287 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:42:55,366 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106716.30, 数量: 6.657, 时间: 1751028172621
2025-06-27 20:42:55,366 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:42:55,799 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:42:56,086 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:42:56,087 - __main__ - WARNING - 未找到 BTCUSDT 的上下文状态数据
2025-06-27 20:42:56,088 - __main__ - INFO - 未找到 BTCUSDT 的上下文状态，将使用REST API初始化订单簿
2025-06-27 20:42:56,088 - __main__ - INFO - 正在从Binance获取 BTCUSDT 订单簿快照...
2025-06-27 20:42:54,157 - __main__ - INFO - WebSocket连接已建立，主线程继续执行
2025-06-27 20:42:54,158 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: False, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:42:54,255 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106716.20, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:42:55,519 - __main__ - INFO - 获取到 BTCUSDT 订单簿快照，最后更新ID: 7895808642121
2025-06-27 20:42:56,441 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106716.10, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:42:56,444 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:42:56,624 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:42:57,393 - __main__ - INFO - 成功保存 BTCUSDT 初始盘口数据到MariaDB，买单: 1000档，卖单: 1000档，更新ID: 7895808642121
2025-06-27 20:42:57,486 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106716.00, 数量: 0.037, 时间: 1751028172686
2025-06-27 20:42:57,573 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:42:58,225 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:42:58,572 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:42:58,663 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:42:58,859 - __main__ - INFO - 成功保存 BTCUSDT 快照数据到Context表
2025-06-27 20:42:58,859 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106715.90, 数量: 0.645, 时间: 1751028172686
2025-06-27 20:42:59,147 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:42:59,228 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:42:59,651 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:42:59,733 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:42:59,918 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106715.50, 数量: 0.004, 时间: 1751028172686
2025-06-27 20:43:00,328 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:43:00,505 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:43:00,505 - __main__ - INFO - 成功更新 BTCUSDT 上下文状态数据
2025-06-27 20:43:01,054 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 20:43:01,598 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 20:43:01,598 - __main__ - INFO - 当前深度缓存大小: 0 条
2025-06-27 20:43:01,599 - __main__ - INFO - 已加载 BTCUSDT 订单簿快照数据，买单: 1000档，卖单: 1000档
2025-06-27 20:43:01,600 - __main__ - INFO - 过滤后的有效更新数量: 0/0
2025-06-27 20:43:01,600 - __main__ - WARNING - 未找到有效的深度更新来初始化 BTCUSDT 订单簿，等待更多数据
2025-06-27 20:43:01,601 - __main__ - INFO - 使用快照数据初始化 BTCUSDT 订单簿，等待后续更新
2025-06-27 20:43:01,759 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106715.40, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:02,689 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106715.00, 数量: 0.167, 时间: 1751028172686
2025-06-27 20:43:03,953 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106714.90, 数量: 0.001, 时间: 1751028172686
2025-06-27 20:43:04,642 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106714.70, 数量: 0.016, 时间: 1751028172686
2025-06-27 20:43:06,376 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106714.50, 数量: 0.013, 时间: 1751028172686
2025-06-27 20:43:07,027 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106714.30, 数量: 0.573, 时间: 1751028172686
2025-06-27 20:43:07,999 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106714.20, 数量: 0.036, 时间: 1751028172686
2025-06-27 20:43:09,336 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106714.10, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:10,317 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106714.00, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:10,900 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106713.80, 数量: 0.039, 时间: 1751028172686
2025-06-27 20:43:11,827 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106713.70, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:12,480 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106713.30, 数量: 0.094, 时间: 1751028172686
2025-06-27 20:43:13,506 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106713.10, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:14,486 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106713.00, 数量: 1.421, 时间: 1751028172686
2025-06-27 20:43:15,168 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106712.70, 数量: 0.001, 时间: 1751028172686
2025-06-27 20:43:15,339 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 0
2025-06-27 20:43:15,861 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106712.50, 数量: 0.025, 时间: 1751028172686
2025-06-27 20:43:16,904 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106712.40, 数量: 0.094, 时间: 1751028172686
2025-06-27 20:43:17,568 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106712.30, 数量: 0.702, 时间: 1751028172686
2025-06-27 20:43:18,509 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106712.20, 数量: 0.095, 时间: 1751028172686
2025-06-27 20:43:19,129 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106712.00, 数量: 0.005, 时间: 1751028172686
2025-06-27 20:43:20,723 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106711.80, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:21,394 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106711.70, 数量: 0.029, 时间: 1751028172686
2025-06-27 20:43:22,412 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106711.60, 数量: 0.113, 时间: 1751028172686
2025-06-27 20:43:23,085 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106711.50, 数量: 0.083, 时间: 1751028172686
2025-06-27 20:43:24,459 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106711.40, 数量: 0.010, 时间: 1751028172686
2025-06-27 20:43:25,116 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106711.30, 数量: 0.034, 时间: 1751028172686
2025-06-27 20:43:25,796 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106711.10, 数量: 0.138, 时间: 1751028172686
2025-06-27 20:43:24,637 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106711.00, 数量: 0.064, 时间: 1751028172686
2025-06-27 20:43:25,285 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.90, 数量: 0.001, 时间: 1751028172686
2025-06-27 20:43:25,888 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.80, 数量: 0.008, 时间: 1751028172686
2025-06-27 20:43:26,826 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.70, 数量: 0.094, 时间: 1751028172686
2025-06-27 20:43:28,706 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.50, 数量: 0.844, 时间: 1751028172686
2025-06-27 20:43:30,086 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.40, 数量: 0.006, 时间: 1751028172686
2025-06-27 20:43:31,057 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.30, 数量: 0.007, 时间: 1751028172686
2025-06-27 20:43:32,382 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.20, 数量: 0.099, 时间: 1751028172686
2025-06-27 20:43:33,696 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.10, 数量: 0.001, 时间: 1751028172686
2025-06-27 20:43:35,031 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106710.00, 数量: 0.062, 时间: 1751028172686
2025-06-27 20:43:35,645 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106709.90, 数量: 0.004, 时间: 1751028172686
2025-06-27 20:43:36,866 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106709.80, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:37,548 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106709.60, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:38,220 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106709.30, 数量: 0.004, 时间: 1751028172686
2025-06-27 20:43:39,203 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106709.10, 数量: 0.002, 时间: 1751028172686
2025-06-27 20:43:40,225 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106709.00, 数量: 0.081, 时间: 1751028172686
2025-06-27 20:43:41,571 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106709.10, 数量: 0.001, 时间: 1751028172689
2025-06-27 20:43:42,274 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106709.00, 数量: 0.556, 时间: 1751028172690
2025-06-27 20:43:42,906 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106708.90, 数量: 0.002, 时间: 1751028172733
2025-06-27 20:43:43,527 - __main__ - INFO - 收到消息时间戳: 2025-06-27 20:42:52.748000, 当前时间戳: 2025-06-27 20:43:43.526603, 时间差: 0:00:50.778603
2025-06-27 20:43:52,379 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:44:49,236 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:45:45,910 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:46:42,568 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:47:40,766 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:48:36,033 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:49:34,284 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:50:31,094 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:51:27,716 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:52:25,495 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:53:22,223 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:54:18,939 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:55:15,799 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:56:13,333 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:57:09,555 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:58:06,629 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 20:59:05,005 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: True, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 21:00:27,551 - __main__ - INFO - MariaDB连接池创建成功
2025-06-27 21:00:27,553 - __main__ - INFO - 开始执行订单簿更新程序
2025-06-27 21:00:27,639 - __main__ - INFO - 已成功创建数据库连接
2025-06-27 21:00:29,420 - __main__ - INFO - MariaDB数据库和表已初始化
2025-06-27 21:00:30,344 - __main__ - INFO - 准备获取最具波动性的交易对
2025-06-27 21:00:31,344 - __main__ - INFO - 正在获取币安期货交易对列表 (第 1/3 次尝试)...
2025-06-27 21:00:33,336 - __main__ - INFO - 获取到 476 个交易中的期货交易对
2025-06-27 21:00:33,337 - __main__ - INFO - 成功获取交易对列表，等待2秒后获取24小时统计...
2025-06-27 21:00:35,352 - __main__ - INFO - 正在获取24小时价格统计 (第 1/3 次尝试)...
2025-06-27 21:00:35,949 - __main__ - INFO - 成功获取到 521 个交易对的24小时统计
2025-06-27 21:00:35,951 - __main__ - INFO - 已获取并排序 475 个交易对的24小时统计
2025-06-27 21:00:35,952 - __main__ - INFO - 排名1: SAHARAUSDT - 波动率: 29.37%, 成交量: 2203700877.87 USDT, 波动率得分: 1378639.42
2025-06-27 21:00:35,952 - __main__ - INFO - 排名2: BANANAS31USDT - 波动率: 31.51%, 成交量: 849541679.99 USDT, 波动率得分: 918418.77
2025-06-27 21:00:35,952 - __main__ - INFO - 排名3: HIFIUSDT - 波动率: 39.61%, 成交量: 206403089.12 USDT, 波动率得分: 569109.49
2025-06-27 21:00:35,952 - __main__ - INFO - 排名4: BSWUSDT - 波动率: 31.46%, 成交量: 258330302.30 USDT, 波动率得分: 505726.15
2025-06-27 21:00:35,953 - __main__ - INFO - 排名5: SEIUSDT - 波动率: 10.75%, 成交量: 566679066.22 USDT, 波动率得分: 255832.57
2025-06-27 21:00:35,953 - __main__ - INFO - 排名6: HUSDT - 波动率: 11.55%, 成交量: 249396546.29 USDT, 波动率得分: 182400.99
2025-06-27 21:00:35,953 - __main__ - INFO - 排名7: XRPUSDT - 波动率: 3.52%, 成交量: 1392481620.44 USDT, 波动率得分: 131165.63
2025-06-27 21:00:35,953 - __main__ - INFO - 排名8: 1000000BOBUSDT - 波动率: 23.14%, 成交量: 23678060.86 USDT, 波动率得分: 112580.03
2025-06-27 21:00:35,954 - __main__ - INFO - 排名9: APTUSDT - 波动率: 6.24%, 成交量: 308563613.68 USDT, 波动率得分: 109594.14
2025-06-27 21:00:35,954 - __main__ - INFO - 排名10: RAREUSDT - 波动率: 5.55%, 成交量: 196988425.36 USDT, 波动率得分: 77895.67
2025-06-27 21:00:35,955 - __main__ - WARNING - REST API调用过于频繁，等待 0.4 秒后再尝试获取交易对列表
2025-06-27 21:00:36,903 - __main__ - INFO - 正在获取币安期货交易对列表 (第 1/3 次尝试)...
2025-06-27 21:00:37,489 - __main__ - INFO - 获取到 476 个交易中的期货交易对
2025-06-27 21:00:37,491 - __main__ - INFO - 正在获取24小时价格统计 (第 1/3 次尝试)...
2025-06-27 21:00:38,357 - __main__ - INFO - 成功获取到 521 个交易对的24小时统计
2025-06-27 21:00:38,358 - __main__ - INFO - 已获取并排序 475 个交易对的24小时统计
2025-06-27 21:00:38,359 - __main__ - INFO - 排名1: SAHARAUSDT - 波动率: 29.31%, 成交量: 2203731829.06 USDT, 波动率得分: 1375832.47
2025-06-27 21:00:38,359 - __main__ - INFO - 排名2: BANANAS31USDT - 波动率: 31.46%, 成交量: 849568437.46 USDT, 波动率得分: 917121.60
2025-06-27 21:00:38,359 - __main__ - INFO - 排名3: HIFIUSDT - 波动率: 39.72%, 成交量: 206426028.03 USDT, 波动率得分: 570606.61
2025-06-27 21:00:38,359 - __main__ - INFO - 排名4: BSWUSDT - 波动率: 31.50%, 成交量: 258333956.73 USDT, 波动率得分: 506260.13
2025-06-27 21:00:38,359 - __main__ - INFO - 排名5: SEIUSDT - 波动率: 10.67%, 成交量: 566687320.30 USDT, 波动率得分: 254025.24
2025-06-27 21:00:38,359 - __main__ - INFO - 排名6: HUSDT - 波动率: 11.60%, 成交量: 249397443.34 USDT, 波动率得分: 183111.98
2025-06-27 21:00:38,360 - __main__ - INFO - 排名7: XRPUSDT - 波动率: 3.53%, 成交量: 1392538435.12 USDT, 波动率得分: 131690.74
2025-06-27 21:00:38,360 - __main__ - INFO - 排名8: 1000000BOBUSDT - 波动率: 23.24%, 成交量: 23689090.22 USDT, 波动率得分: 113112.43
2025-06-27 21:00:38,360 - __main__ - INFO - 排名9: APTUSDT - 波动率: 6.25%, 成交量: 308588621.00 USDT, 波动率得分: 109739.12
2025-06-27 21:00:38,360 - __main__ - INFO - 排名10: RAREUSDT - 波动率: 5.55%, 成交量: 196988440.19 USDT, 波动率得分: 77895.68
2025-06-27 21:00:38,361 - __main__ - INFO - 当前波动最大的交易对为: SAHARAUSDT, 24小时价格变化: 29.31%, 成交量: 2203731829.06 USDT
2025-06-27 21:00:38,361 - __main__ - INFO - 选择波动性最大的交易对: BTCUSDT
2025-06-27 21:00:38,361 - __main__ - INFO - 初始交易对设置为: BTCUSDT
2025-06-27 21:00:38,362 - __main__ - INFO - 正在创建WebSocket连接，准备订阅 BTCUSDT
2025-06-27 21:00:38,362 - __main__ - INFO - 数据库处理线程已启动
2025-06-27 21:00:38,363 - __main__ - INFO - 数据库线程初始化时尝试立即保存一次
2025-06-27 21:00:38,363 - __main__ - INFO - 开始调度快照生成任务，每500毫秒生成一次订单簿快照
2025-06-27 21:00:38,363 - __main__ - INFO - 快照生成线程已启动
2025-06-27 21:00:38,363 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 0
2025-06-27 21:00:38,363 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:38,364 - __main__ - INFO - 等待WebSocket连接建立... (尝试 1/5)
2025-06-27 21:00:38,364 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:38,364 - __main__ - WARNING - 没有深度快照数据需要保存 - 检查take_snapshot函数是否正常运行
2025-06-27 21:00:38,680 - websocket - INFO - Websocket connected
2025-06-27 21:00:38,681 - __main__ - INFO - 已连接到Binance WebSocket，准备订阅 BTCUSDT 数据
2025-06-27 21:00:38,682 - __main__ - INFO - 已发送 BTCUSDT 订阅请求: 深度更新和聚合交易
2025-06-27 21:00:38,682 - __main__ - INFO - 尝试初始化订单簿 (第 1/5 次)
2025-06-27 21:00:38,682 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 21:00:38,892 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:38,893 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:38,905 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106628.00, 数量: 0.006, 时间: 1751029237910
2025-06-27 21:00:39,502 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:39,638 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:39,784 - __main__ - INFO - 成功加载 BTCUSDT 的上下文状态数据，最后更新ID: 0
2025-06-27 21:00:39,784 - __main__ - INFO - 成功从保存的状态恢复 SOLUSDT 订单簿，买单: 0档，卖单: 0档，最后更新ID: 0
2025-06-27 21:00:39,784 - __main__ - INFO - 成功从上下文状态恢复 BTCUSDT 订单簿，跳过REST API初始化
2025-06-27 21:00:39,784 - __main__ - INFO - 订单簿初始化成功
2025-06-27 21:00:40,036 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:40,036 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:40,141 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106627.90, 数量: 0.030, 时间: 1751029237998
2025-06-27 21:00:40,393 - __main__ - INFO - WebSocket连接已建立，主线程继续执行
2025-06-27 21:00:40,394 - __main__ - INFO - 当前交易对: BTCUSDT, 订单簿初始化: False, 缓存的深度更新: 0, 快照数量: 0
2025-06-27 21:00:40,577 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:40,658 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:41,149 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:41,150 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.071000, 当前时间戳: 2025-06-27 21:00:41.150317, 时间差: 0:00:03.079317
2025-06-27 21:00:41,150 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:41,814 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:42,977 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:43,031 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:43,032 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:43,496 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:00:43,581 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:43,674 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:44,174 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:44,177 - __main__ - INFO - 大量深度更新: btcusdt 有 55 个买单和 73 个卖单更新, ID从 0 更新到 7895911366498
2025-06-27 21:00:44,179 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:44.178 [时间戳:1751029244178], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 116.09, 卖盘总量: 79.62, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 21:00:44,179 - __main__ - INFO - 收到第一个 btcusdt 深度更新 (U=7895911356661, u=7895911366498)，开始初始化订单簿
2025-06-27 21:00:44,181 - __main__ - INFO - 尝试初始化订单簿 (第 1/5 次)
2025-06-27 21:00:44,181 - __main__ - INFO - 尝试从上下文状态恢复 BTCUSDT 订单簿
2025-06-27 21:00:44,329 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.178000, 当前时间戳: 2025-06-27 21:00:44.329076, 时间差: 0:00:06.151076
2025-06-27 21:00:44,885 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=45，卖单数=58
2025-06-27 21:00:45,391 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:45.391 [时间戳:1751029245391], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 116.09, 卖盘总量: 79.62, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 21:00:46,240 - __main__ - INFO - 成功加载 BTCUSDT 的上下文状态数据，最后更新ID: 0
2025-06-27 21:00:46,241 - __main__ - INFO - 成功从保存的状态恢复 SOLUSDT 订单簿，买单: 0档，卖单: 0档，最后更新ID: 0
2025-06-27 21:00:46,241 - __main__ - INFO - 成功从上下文状态恢复 BTCUSDT 订单簿，跳过REST API初始化
2025-06-27 21:00:46,241 - __main__ - INFO - 订单簿初始化成功
2025-06-27 21:00:46,291 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:46,300 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:46,981 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=0，卖单数=0
2025-06-27 21:00:46,981 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:00:46,981 - __main__ - WARNING - 订单簿数据完全为空，无法生成快照 - 买单：0档, 卖单：0档
2025-06-27 21:00:47,308 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106628.00, 数量: 0.026, 时间: 1751029238066
2025-06-27 21:00:47,515 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=34，卖单数=34
2025-06-27 21:00:47,516 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:47.516 [时间戳:1751029247516], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 49.71, 卖盘总量: 83.35, 点差: 0.0001%, 当前深度快照数量: 3
2025-06-27 21:00:48,043 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=34，卖单数=34
2025-06-27 21:00:48,043 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:48.043 [时间戳:1751029248043], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 49.71, 卖盘总量: 83.35, 点差: 0.0001%, 当前深度快照数量: 4
2025-06-27 21:00:48,566 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=34，卖单数=34
2025-06-27 21:00:48,566 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:48.566 [时间戳:1751029248566], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 49.71, 卖盘总量: 83.35, 点差: 0.0001%, 当前深度快照数量: 5
2025-06-27 21:00:49,104 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=34，卖单数=34
2025-06-27 21:00:49,105 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:49.105 [时间戳:1751029249105], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 49.71, 卖盘总量: 83.35, 点差: 0.0001%, 当前深度快照数量: 6
2025-06-27 21:00:49,640 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=34，卖单数=34
2025-06-27 21:00:49,641 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:49.640 [时间戳:1751029249640], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 49.71, 卖盘总量: 83.35, 点差: 0.0001%, 当前深度快照数量: 7
2025-06-27 21:00:50,182 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=34，卖单数=34
2025-06-27 21:00:50,183 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:50.183 [时间戳:1751029250183], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 49.71, 卖盘总量: 83.35, 点差: 0.0001%, 当前深度快照数量: 8
2025-06-27 21:00:50,545 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.278000, 当前时间戳: 2025-06-27 21:00:50.545644, 时间差: 0:00:12.267644
2025-06-27 21:00:50,691 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=34，卖单数=34
2025-06-27 21:00:50,691 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:50.691 [时间戳:1751029250691], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 49.71, 卖盘总量: 83.35, 点差: 0.0001%, 当前深度快照数量: 9
2025-06-27 21:00:51,214 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=34，卖单数=34
2025-06-27 21:00:50,131 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:50.131 [时间戳:1751029250131], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 49.71, 卖盘总量: 83.35, 点差: 0.0001%, 当前深度快照数量: 10
2025-06-27 21:00:50,308 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:00:50,966 - __main__ - INFO - 大量深度更新: btcusdt 有 51 个买单和 47 个卖单更新, ID从 7895911374632 更新到 7895911381555
2025-06-27 21:00:50,967 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106627.90, 数量: 0.002, 时间: 1751029238310
2025-06-27 21:00:51,654 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.381000, 当前时间戳: 2025-06-27 21:00:51.654631, 时间差: 0:00:13.273631
2025-06-27 21:00:52,073 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=63，卖单数=61
2025-06-27 21:00:52,150 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:52.150 [时间戳:1751029252150], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 43.76, 卖盘总量: 34.05, 点差: 0.0001%, 当前深度快照数量: 11
2025-06-27 21:00:53,017 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=63，卖单数=61
2025-06-27 21:00:53,164 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:53.164 [时间戳:1751029253164], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 43.76, 卖盘总量: 34.05, 点差: 0.0001%, 当前深度快照数量: 12
2025-06-27 21:00:53,310 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:00:53,750 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=63，卖单数=61
2025-06-27 21:00:53,751 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:53.751 [时间戳:1751029253751], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 43.76, 卖盘总量: 34.05, 点差: 0.0001%, 当前深度快照数量: 13
2025-06-27 21:00:53,885 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106628.00, 数量: 0.006, 时间: 1751029238344
2025-06-27 21:00:54,295 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=87，卖单数=74
2025-06-27 21:00:54,296 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:54.296 [时间戳:1751029254296], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 32.59, 卖盘总量: 32.86, 点差: 0.0001%, 当前深度快照数量: 14
2025-06-27 21:00:54,822 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=87，卖单数=74
2025-06-27 21:00:54,823 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.483000, 当前时间戳: 2025-06-27 21:00:54.823206, 时间差: 0:00:16.340206
2025-06-27 21:00:54,823 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:54.823 [时间戳:1751029254823], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 32.59, 卖盘总量: 32.86, 点差: 0.0001%, 当前深度快照数量: 15
2025-06-27 21:00:55,335 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=87，卖单数=74
2025-06-27 21:00:55,336 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:55.336 [时间戳:1751029255336], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 32.59, 卖盘总量: 32.86, 点差: 0.0001%, 当前深度快照数量: 16
2025-06-27 21:00:55,888 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=87，卖单数=74
2025-06-27 21:00:55,889 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:55.889 [时间戳:1751029255889], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 32.59, 卖盘总量: 32.86, 点差: 0.0001%, 当前深度快照数量: 17
2025-06-27 21:00:56,052 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:00:56,386 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.581000, 当前时间戳: 2025-06-27 21:00:56.386928, 时间差: 0:00:17.805928
2025-06-27 21:00:56,458 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=98，卖单数=86
2025-06-27 21:00:56,531 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:56.531 [时间戳:1751029256531], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.20, 卖盘总量: 35.92, 点差: 0.0001%, 当前深度快照数量: 18
2025-06-27 21:00:57,589 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=98，卖单数=86
2025-06-27 21:00:57,757 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:57.757 [时间戳:1751029257757], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.20, 卖盘总量: 35.92, 点差: 0.0001%, 当前深度快照数量: 19
2025-06-27 21:00:57,936 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:00:58,114 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=98，卖单数=86
2025-06-27 21:00:58,115 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:58.115 [时间戳:1751029258115], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.20, 卖盘总量: 35.92, 点差: 0.0001%, 当前深度快照数量: 20
2025-06-27 21:00:58,618 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 20
2025-06-27 21:00:58,619 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=98，卖单数=86
2025-06-27 21:00:58,619 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106627.90, 数量: 0.001, 时间: 1751029238430
2025-06-27 21:00:58,620 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:58.620 [时间戳:1751029258620], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.61, 卖盘总量: 34.45, 点差: 0.0001%, 当前深度快照数量: 21
2025-06-27 21:00:59,104 - __main__ - INFO - 准备保存 21 条快照数据，并已清空全局快照列表
2025-06-27 21:00:59,193 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 0，订单簿状态: 初始化=False，买单数=123，卖单数=102
2025-06-27 21:00:59,612 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:59.612 [时间戳:1751029259612], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.61, 卖盘总量: 34.45, 点差: 0.0001%, 当前深度快照数量: 1
2025-06-27 21:00:59,863 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 1，订单簿状态: 初始化=False，买单数=123，卖单数=102
2025-06-27 21:00:59,951 - __main__ - INFO - 成功保存 21 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 21:00:59,951 - __main__ - INFO - 深度快照已生成: solusdt @ 21:00:59.951 [时间戳:1751029259951], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.61, 卖盘总量: 34.45, 点差: 0.0001%, 当前深度快照数量: 2
2025-06-27 21:01:00,206 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.682000, 当前时间戳: 2025-06-27 21:01:00.206552, 时间差: 0:00:21.524552
2025-06-27 21:01:00,406 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 2，订单簿状态: 初始化=False，买单数=123，卖单数=102
2025-06-27 21:01:00,407 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:00.407 [时间戳:1751029260407], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.61, 卖盘总量: 34.45, 点差: 0.0001%, 当前深度快照数量: 3
2025-06-27 21:01:00,719 - __main__ - INFO - 共保存了 21 条深度快照数据到MariaDB
2025-06-27 21:01:01,263 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 3，订单簿状态: 初始化=False，买单数=123，卖单数=102
2025-06-27 21:01:01,264 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:01.264 [时间戳:1751029261264], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.61, 卖盘总量: 34.45, 点差: 0.0001%, 当前深度快照数量: 4
2025-06-27 21:01:01,767 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 4，订单簿状态: 初始化=False，买单数=123，卖单数=102
2025-06-27 21:01:01,767 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:01:01,767 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:01.767 [时间戳:1751029261767], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.61, 卖盘总量: 34.45, 点差: 0.0001%, 当前深度快照数量: 5
2025-06-27 21:01:02,117 - __main__ - INFO - 大量深度更新: btcusdt 有 35 个买单和 53 个卖单更新, ID从 7895911406015 更新到 7895911412354
2025-06-27 21:01:02,117 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106627.90, 数量: 0.001, 时间: 1751029238704
2025-06-27 21:01:02,268 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 5，订单簿状态: 初始化=False，买单数=139，卖单数=125
2025-06-27 21:01:02,268 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:02.268 [时间戳:1751029262268], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.08, 卖盘总量: 83.16, 点差: 0.0001%, 当前深度快照数量: 6
2025-06-27 21:01:03,041 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 6，订单簿状态: 初始化=False，买单数=139，卖单数=125
2025-06-27 21:01:03,042 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.787000, 当前时间戳: 2025-06-27 21:01:03.042342, 时间差: 0:00:24.255342
2025-06-27 21:01:03,042 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:03.042 [时间戳:1751029263042], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.08, 卖盘总量: 83.16, 点差: 0.0001%, 当前深度快照数量: 7
2025-06-27 21:01:03,866 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 7，订单簿状态: 初始化=False，买单数=139，卖单数=125
2025-06-27 21:01:03,866 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:03.866 [时间戳:1751029263866], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.08, 卖盘总量: 83.16, 点差: 0.0001%, 当前深度快照数量: 8
2025-06-27 21:01:04,431 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 8，订单簿状态: 初始化=False，买单数=139，卖单数=125
2025-06-27 21:01:04,432 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:04.432 [时间戳:1751029264432], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.08, 卖盘总量: 83.16, 点差: 0.0001%, 当前深度快照数量: 9
2025-06-27 21:01:04,890 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:01:04,963 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 9，订单簿状态: 初始化=False，买单数=139，卖单数=125
2025-06-27 21:01:05,038 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:05.038 [时间戳:1751029265038], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 31.08, 卖盘总量: 83.16, 点差: 0.0001%, 当前深度快照数量: 10
2025-06-27 21:01:05,185 - __main__ - INFO - 大量深度更新: btcusdt 有 89 个买单和 165 个卖单更新, ID从 7895911412354 更新到 7895911427362
2025-06-27 21:01:05,186 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106628.00, 数量: 0.003, 时间: 1751029238730
2025-06-27 21:01:05,532 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 10，订单簿状态: 初始化=False，买单数=175，卖单数=208
2025-06-27 21:01:05,704 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:05.704 [时间戳:1751029265704], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 26.38, 卖盘总量: 22.83, 点差: 0.0001%, 当前深度快照数量: 11
2025-06-27 21:01:05,865 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.888000, 当前时间戳: 2025-06-27 21:01:05.864978, 时间差: 0:00:26.976978
2025-06-27 21:01:06,035 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 11，订单簿状态: 初始化=False，买单数=175，卖单数=208
2025-06-27 21:01:06,036 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:06.036 [时间戳:1751029266036], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 26.38, 卖盘总量: 22.83, 点差: 0.0001%, 当前深度快照数量: 12
2025-06-27 21:01:06,612 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 12，订单簿状态: 初始化=False，买单数=175，卖单数=208
2025-06-27 21:01:06,706 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:06.706 [时间戳:1751029266706], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 26.38, 卖盘总量: 22.83, 点差: 0.0001%, 当前深度快照数量: 13
2025-06-27 21:01:07,211 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 13，订单簿状态: 初始化=False，买单数=175，卖单数=208
2025-06-27 21:01:07,723 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:07.722 [时间戳:1751029267722], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 26.38, 卖盘总量: 22.83, 点差: 0.0001%, 当前深度快照数量: 14
2025-06-27 21:01:07,774 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 14，订单簿状态: 初始化=False，买单数=175，卖单数=208
2025-06-27 21:01:07,775 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:07.775 [时间戳:1751029267775], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 26.38, 卖盘总量: 22.83, 点差: 0.0001%, 当前深度快照数量: 15
2025-06-27 21:01:07,894 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:01:08,234 - __main__ - INFO - 大量深度更新: btcusdt 有 59 个买单和 97 个卖单更新, ID从 7895911427362 更新到 7895911442849
2025-06-27 21:01:08,235 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:38.986000, 当前时间戳: 2025-06-27 21:01:08.235170, 时间差: 0:00:29.249170
2025-06-27 21:01:08,943 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 15，订单簿状态: 初始化=False，买单数=198，卖单数=262
2025-06-27 21:01:09,016 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:09.016 [时间戳:1751029269016], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.63, 卖盘总量: 18.62, 点差: 0.0001%, 当前深度快照数量: 16
2025-06-27 21:01:09,016 - __main__ - INFO - 快照调度状态：过去30秒新增了 47 个快照，当前总计 16 个快照
2025-06-27 21:01:09,453 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 16，订单簿状态: 初始化=False，买单数=198，卖单数=262
2025-06-27 21:01:09,624 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:09.624 [时间戳:1751029269624], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.63, 卖盘总量: 18.62, 点差: 0.0001%, 当前深度快照数量: 17
2025-06-27 21:01:09,794 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:01:09,953 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 17，订单簿状态: 初始化=False，买单数=198，卖单数=262
2025-06-27 21:01:09,954 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:09.954 [时间戳:1751029269954], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.63, 卖盘总量: 18.62, 点差: 0.0001%, 当前深度快照数量: 18
2025-06-27 21:01:10,123 - __main__ - INFO - 大量深度更新: btcusdt 有 47 个买单和 57 个卖单更新, ID从 7895911442849 更新到 7895911454454
2025-06-27 21:01:10,123 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:39.090000, 当前时间戳: 2025-06-27 21:01:10.123848, 时间差: 0:00:31.033848
2025-06-27 21:01:10,514 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 18，订单簿状态: 初始化=False，买单数=216，卖单数=272
2025-06-27 21:01:10,589 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:10.589 [时间戳:1751029270589], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.93, 卖盘总量: 19.12, 点差: 0.0001%, 当前深度快照数量: 19
2025-06-27 21:01:11,040 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 19，订单簿状态: 初始化=False，买单数=216，卖单数=272
2025-06-27 21:01:11,194 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:11.193 [时间戳:1751029271193], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.93, 卖盘总量: 19.12, 点差: 0.0001%, 当前深度快照数量: 20
2025-06-27 21:01:11,353 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:01:11,557 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 20，订单簿状态: 初始化=False，买单数=216，卖单数=272
2025-06-27 21:01:11,557 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:11.557 [时间戳:1751029271557], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.93, 卖盘总量: 19.12, 点差: 0.0001%, 当前深度快照数量: 21
2025-06-27 21:01:11,963 - __main__ - INFO - 收到聚合交易: BTCUSDT, 价格: 106628.00, 数量: 0.003, 时间: 1751029239023
2025-06-27 21:01:12,097 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 21，订单簿状态: 初始化=False，买单数=238，卖单数=297
2025-06-27 21:01:12,097 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:12.097 [时间戳:1751029272097], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.78, 卖盘总量: 20.18, 点差: 0.0001%, 当前深度快照数量: 22
2025-06-27 21:01:12,499 - __main__ - INFO - 收到消息时间戳: 2025-06-27 21:00:39.198000, 当前时间戳: 2025-06-27 21:01:12.499842, 时间差: 0:00:33.301842
2025-06-27 21:01:12,655 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 22，订单簿状态: 初始化=False，买单数=238，卖单数=297
2025-06-27 21:01:12,655 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:12.655 [时间戳:1751029272655], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.78, 卖盘总量: 20.18, 点差: 0.0001%, 当前深度快照数量: 23
2025-06-27 21:01:13,182 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 23，订单簿状态: 初始化=False，买单数=238，卖单数=297
2025-06-27 21:01:13,354 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:13.354 [时间戳:1751029273354], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.78, 卖盘总量: 20.18, 点差: 0.0001%, 当前深度快照数量: 24
2025-06-27 21:01:13,522 - __main__ - INFO - 成功保存 BTCUSDT 更新数据到Context表
2025-06-27 21:01:13,706 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 24，订单簿状态: 初始化=False，买单数=238，卖单数=297
2025-06-27 21:01:13,707 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:13.707 [时间戳:1751029273707], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.78, 卖盘总量: 20.18, 点差: 0.0001%, 当前深度快照数量: 25
2025-06-27 21:01:14,368 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 25，订单簿状态: 初始化=False，买单数=238，卖单数=297
2025-06-27 21:01:14,370 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:14.370 [时间戳:1751029274370], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.78, 卖盘总量: 20.18, 点差: 0.0001%, 当前深度快照数量: 26
2025-06-27 21:01:14,961 - __main__ - INFO - 尝试生成快照 - 当前深度快照数量: 26，订单簿状态: 初始化=False，买单数=238，卖单数=297
2025-06-27 21:01:14,961 - __main__ - INFO - 成功更新 BTCUSDT 上下文状态数据
2025-06-27 21:01:14,962 - __main__ - INFO - 深度快照已生成: solusdt @ 21:01:14.962 [时间戳:1751029274962], 买盘最高价: 106627.9, 卖盘最低价: 106628.0, 买盘总量: 25.78, 卖盘总量: 20.18, 点差: 0.0001%, 当前深度快照数量: 27
2025-06-27 21:01:15,158 - __main__ - INFO - 收到信号 2，准备关闭程序...
2025-06-27 21:01:15,294 - __main__ - INFO - 大量深度更新: btcusdt 有 48 个买单和 56 个卖单更新, ID从 7895911463674 更新到 7895911477650
2025-06-27 21:01:15,295 - __main__ - WARNING - WebSocket连接关闭: None None
2025-06-27 21:01:15,296 - __main__ - INFO - 触发WebSocket重连...
2025-06-27 21:01:15,296 - __main__ - INFO - WebSocket线程结束
2025-06-27 21:01:15,375 - __main__ - INFO - 开始保存数据到MariaDB - 当前深度快照数量: 27
2025-06-27 21:01:15,375 - __main__ - INFO - 准备保存 27 条快照数据，并已清空全局快照列表
2025-06-27 21:01:15,448 - __main__ - INFO - 数据库处理线程已结束
2025-06-27 21:01:16,333 - __main__ - INFO - 成功保存 27 条 SOLUSDT 深度快照数据到MariaDB
2025-06-27 21:01:16,939 - __main__ - INFO - 共保存了 27 条深度快照数据到MariaDB
2025-06-27 21:01:16,939 - __main__ - INFO - 程序已关闭
2025-06-27 21:01:17,310 - __main__ - INFO - 数据库连接已关闭
2025-06-27 21:01:17,310 - __main__ - INFO - 程序执行完毕
